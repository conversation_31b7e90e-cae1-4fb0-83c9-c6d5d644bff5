<div class="card">
    <div class="card-header ">
        <div class="row">
            <div class="col-md-4">
                <h5 class="mb-0">Penalties Receivable Report</h5>
            </div>
            <div class="col-md-8 d-flex justify-content-end">
                <x-date-filter />
                <x-export-buttons :with-excel="true" />
            </div>
        </div>
    </div>

    <div class="card-body">
        <table id="report-table" class="table table-bordered">
            <thead>
                <tr>
                    <th>Loan #</th>
                    <th class="text-start">Customer</th>
                    <th class="text-end">Phone Number</th>
                    <th class="text-start">Loan Amount</th>
                    <th class="text-end">Penalties</th>
                    <th class="text-end">Penalties Receivable</th>
                    <th class="text-end">Penalties Paid</th>
                    <th class="text-end">Penalties Pending</th>
                </tr>
            </thead>
            <tbody>
                @forelse ($records as $record)
                    <tr>
                        <td>{{ $record->id }}</td>
                        <td>{{ $record->customer->name }}</td>
                        <td class="text-end">{{ $record->customer->Telephone_Number }}</td>
                        <td class="text-end">{{ number_format($record->Credit_Amount) }}</td>
                        <td class="text-end">{{ number_format($record->penalties_sum_amount__to__pay) }}</td>
                        <td class="text-end">{{ number_format($record->penaltiesReceivable()) }}</td>
                        <td class="text-end">{{ number_format($record->penaltiesPaid()) }}</td>
                        <td class="text-end">
                            {{ number_format($record->penaltiesReceivable() - $record->penaltiesPaid()) }}</td>
                    </tr>
                @empty
                    <tr>
                        <td colspan="6" class="text-center">No records found</td>
                    </tr>
                @endforelse
            </tbody>
            <tfoot>
                @php
                    $penaltiesReceivableSum = $records->sum(fn($record) => $record->penaltiesReceivable());
                    $penaltiesPaidSum = $records->sum(fn($record) => $record->penaltiesPaid());
                @endphp
                <tr>
                    <th>Totals</th>
                    <th class="text-end">{{ $records->count() }}</th>
                    <th></th>
                    <th class="text-end"><x-money :value="$records->sum('Credit_Amount')" /></th>
                    <th class="text-end"><x-money :value="$records->sum('penalties_sum_amount__to__pay')" /></th>
                    <th class="text-end"><x-money :value="$penaltiesReceivableSum" /></th>
                    <th class="text-end"><x-money :value="$penaltiesPaidSum" /></th>
                    <th class="text-end"><x-money :value="$penaltiesReceivableSum - $penaltiesPaidSum" /></th>
                </tr>
            </tfoot>
        </table>

        <div class="pagination mt-5 d-flex justify-content-end">
            {{ $records->links() }}
        </div>
    </div>
</div>
