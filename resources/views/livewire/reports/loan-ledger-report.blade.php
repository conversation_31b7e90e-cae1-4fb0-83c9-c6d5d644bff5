<div>
    <!-- Date Filters -->
    <div class="row mb-3">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label for="startDate" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="startDate" wire:model="startDate">
                        </div>
                        <div class="col-md-3">
                            <label for="endDate" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="endDate" wire:model="endDate">
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary btn-sm" wire:click="applyDateFilters">
                                    <i class="bx bx-filter"></i> Apply Filters
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" wire:click="clearDateFilters">
                                    <i class="bx bx-x"></i> Clear Filters
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" wire:click="printReport">
                                    <i class="bx bx-printer"></i> Export to Pdf
                                </button>
                            </div>
                        </div>
                    </div>
                    @if($startDate || $endDate)
                        <div class="mt-2">
                            <small class="text-muted">
                                <i class="bx bx-info-circle"></i>
                                Showing transactions
                                @if($startDate && $endDate)
                                    from {{ \Carbon\Carbon::parse($startDate)->format('d M Y') }} to {{ \Carbon\Carbon::parse($endDate)->format('d M Y') }}
                                @elseif($startDate)
                                    from {{ \Carbon\Carbon::parse($startDate)->format('d M Y') }} onwards
                                @elseif($endDate)
                                    up to {{ \Carbon\Carbon::parse($endDate)->format('d M Y') }}
                                @endif
                            </small>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Ledger Table -->
    <div class="card">
        <div class="card-header">
            <h6 class="card-title mb-0">Transaction History</h6>
        </div>
        <div class="card-body p-0">
            @if($records->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Date</th>
                                <th>Description</th>
                                <th class="text-end">Debit</th>
                                <th class="text-end">Credit</th>
                                <th class="text-end">Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php $runningBalance = 0; @endphp
                            @foreach($records as $index => $entry)
                                @php
                                    // Calculate running balance
                                    if ($entry->accounting_type === 'debit') {
                                        $runningBalance += $entry->debit_amount ?? $entry->amount;
                                    } else {
                                        $runningBalance -= $entry->credit_amount ?? $entry->amount;
                                    }
                                @endphp
                                <tr>
                                    <td>{{ $entry->created_at->format('d M Y H:i:s') }}</td>
                                    <td>{{ $entry->account_name }}</td>
                                    <td class="text-end">
                                        @if($entry->debit_amount > 0)
                                            <span>
                                                {{ number_format($entry->debit_amount, 2) }}
                                            </span>
                                        @else
                                            <span>-</span>
                                        @endif
                                    </td>
                                    <td class="text-end">
                                        @if($entry->credit_amount > 0)
                                            <span>
                                                {{ number_format($entry->credit_amount, 2) }}
                                            </span>
                                        @else
                                            <span>-</span>
                                        @endif
                                    </td>
                                    <td class="text-end">
                                        {{ number_format(abs($runningBalance), 2) }}
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot class="table-light">
                            <tr>
                                <th colspan="2">Total</th>
                                <th class="text-end">
                                    {{ number_format($records->sum('debit_amount'), 2) }}
                                </th>
                                <th class="text-end">
                                    {{ number_format($records->sum('credit_amount'), 2) }}
                                </th>
                                <th class="text-end">
                                    <strong>
                                        {{ number_format(abs($runningBalance), 2) }}
                                    </strong>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="card-footer">
                    {{ $records->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="bx bx-receipt bx-lg text-muted"></i>
                    <h6 class="mt-2">No Transactions Found</h6>
                    <p class="text-muted">
                        @if($startDate || $endDate)
                            No transactions found for the selected date range.
                        @else
                            No transactions have been recorded for this loan account yet.
                        @endif
                    </p>
                </div>
            @endif
        </div>
    </div>
</div>
