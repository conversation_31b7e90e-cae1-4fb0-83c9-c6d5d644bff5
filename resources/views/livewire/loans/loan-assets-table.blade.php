<div>
    <div class="card">
        <div class="card-header">
            <div class="row">
                <div class="col-md-6 d-flex justify-content-start gap-3">
                    <div class="form-group">
                        <label for="search">Search</label>
                        <input type="search" class="form-control" id="search" wire:model.live="search">
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" id="status" wire:model.live="assetStatus">
                            <option value="">All</option>
                            <option value="Active">Active</option>
                            <option value="Reposessed">Reposessed</option>
                            <option value="Disposed">Disposed</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr class="">
                            <th>Loan #</th>
                            <th>Customer</th>
                            <th class="text-end">Phone Number</th>
                            <th>Identification</th>
                            <th>Location</th>
                            <th>Status</th>
                            <th class="text-end">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($records as $record)
                            <tr>
                                <td>{{ $record->loan->id }}</td>
                                <td>{{ $record->customer->name }}</td>
                                <td class="text-end">{{ $record->customer->Telephone_Number }}</td>
                                <td>{{ $record->Identification }}</td>
                                <td>{{ $record->location }}</td>
                                <td>{{ $record->Status }}</td>
                                <td class="text-end">


                                    <div class="dropdown">
                                        <button class="btn btn-outline-dark btn-sm dropdown-toggle" type="button"
                                            data-bs-toggle="dropdown" aria-expanded="false">
                                            Actions
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end dropdown-menu-lg-start">
                                            @if (empty($record->Reposessed_Date))
                                                <li>
                                                    <button
                                                        wire:click="reposess('{{ $record->Identification }}', '{{ $record->loan->id }}')"
                                                        class="dropdown-item">
                                                        <span class="me-2"><svg xmlns="http://www.w3.org/2000/svg"
                                                                width="15" height="15" viewBox="0 0 24 24"
                                                                fill="none" stroke="currentColor" stroke-width="2"
                                                                stroke-linecap="round" stroke-linejoin="round"
                                                                class="lucide lucide-undo2-icon lucide-undo-2">
                                                                <path d="M9 14 4 9l5-5" />
                                                                <path
                                                                    d="M4 9h10.5a5.5 5.5 0 0 1 5.5 5.5a5.5 5.5 0 0 1-5.5 5.5H11" />
                                                            </svg></span>
                                                        <span>Reposess</span>
                                                    </button>
                                                </li>
                                            @endif
                                            <!-- Modal Trigger -->
                                            @if ($record->Status == 'Reposessed')
                                                <li>
                                                    <button class="dropdown-item" data-bs-toggle="modal"
                                                        data-bs-target="#loanClosureModal"
                                                        data-loan-id="{{ $record->loan->id }}"
                                                        data-outstanding-balance="{{ $record->loan->totalOutstandingBalance() }}">
                                                        <span>Loan Closure</span>
                                                    </button>
                                                </li>
                                            @endif
                                            <li>
                                                <button
                                                    wire:click="refreshLocation('{{ $record->Identification }}', '{{ $record->loan->id }}')"
                                                    class="dropdown-item">
                                                    <span class="me-2"><svg xmlns="http://www.w3.org/2000/svg"
                                                            width="15" height="15" viewBox="0 0 24 24"
                                                            fill="none" stroke="currentColor" stroke-width="2"
                                                            stroke-linecap="round" stroke-linejoin="round"
                                                            class="lucide lucide-refresh-cw-icon lucide-refresh-cw">
                                                            <path
                                                                d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                                                            <path d="M21 3v5h-5" />
                                                            <path
                                                                d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                                                            <path d="M8 16H3v5" />
                                                        </svg></span>
                                                    <span>Refresh Location</span>
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>

            </div>
        </div>
    </div>

    <!-- Bootstrap Modal -->
    <div class="modal fade" id="loanClosureModal" tabindex="-1" aria-labelledby="loanClosureModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="loanClosureModalLabel">Loan Closure</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="loanClosureForm" method="POST" action="{{ route('loan.close') }}">
                    @csrf
                    <input type="hidden" name="loan_id" id="modalLoanId">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="disposalIncome" class="form-label">Disposal Income</label>
                            <input type="number" step="0.01" class="form-control" id="disposalIncome"
                                name="disposal_income" required>
                        </div>
                        <div class="mb-3">
                            <label for="disposalCosts" class="form-label">Disposal Costs</label>
                            <input type="number" step="0.01" class="form-control" id="disposalCosts"
                                name="disposal_costs" required>
                        </div>
                        <div class="mb-3">
                            <label for="outstandingBalance" class="form-label">Outstanding Balance</label>
                            <input type="number" step="0.01" class="form-control" id="modalOutstandingBalance"
                                readonly>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        &nbsp;
                        <button type="submit" class="btn btn-primary">Submit</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {{-- @livewire('loan-closure-modal') --}}
</div>
{{-- <script>
    document.addEventListener('livewire:initialized', () => {
        Livewire.on('openLoanClosureModal', (event) => {
            // Try to find existing component
            let component = Livewire.find('loan-closure-modal');

            if (!component) {
                // If not found, dynamically add it
                Livewire.dispatch('load-loan-closure-modal');
                component = Livewire.find('loan-closure-modal');
            }

            if (component) {
                component.openModal(event.loanId);
            } else {
                console.error('Failed to load LoanClosureModal component');
            }
        });
    });
</script> --}}

<script>
    // Initialize modal with data when shown
    document.getElementById('loanClosureModal').addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const loanId = button.getAttribute('data-loan-id');
        const outstandingBalance = button.getAttribute('data-outstanding-balance');

        const modal = this;
        modal.querySelector('#modalLoanId').value = loanId;
        modal.querySelector('#modalOutstandingBalance').value = outstandingBalance;
    });

    // Handle form submission
    document.getElementById('loanClosureForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const form = this;
        const formData = new FormData(form);
        const disposalIncome = parseFloat(formData.get('disposal_income'));
        const disposalCosts = parseFloat(formData.get('disposal_costs'));
        const outstandingBalance = parseFloat(document.getElementById('modalOutstandingBalance').value);
        const totalRecovered = disposalIncome - disposalCosts;

        // Determine the closure type for the confirmation message
        let closureType, message;

        if (totalRecovered >= outstandingBalance) {
            closureType = "Fully Paid Off";
            message = `This will mark the loan as fully paid off. Are you sure you want to proceed?`;
        } else {
            closureType = "Written Off";
            const writtenOffAmount = outstandingBalance - totalRecovered;
            message =
                `This will result in writing off ${writtenOffAmount.toFixed(2)}. Are you sure you want to proceed?`;
        }

        // Show confirmation dialog
        if (confirm(`${message}\n\nClosure Type: ${closureType}`)) {
            // User confirmed - proceed with submission
            fetch(form.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success message
                        alert(data.message);
                        // Close modal
                        bootstrap.Modal.getInstance(document.getElementById('loanClosureModal')).hide();
                        // Optionally refresh the page or update the table
                        window.location.reload();
                    } else {
                        alert(data.message || 'An error occurred');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while processing your request');
                });
        }
        // If user cancels, do nothing
    });
</script>
