@extends('layouts/contentNavbarLayout')
@section('title', 'Cash Sale - Create')
@section('content')

    <div class="container">
        <div class="card">
            <div class="card-header">
                <h3>Record New Cash Sale</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('cash-sales.store') }}" enctype="multipart/form-data">
                    @csrf

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="customer_name" class="form-label">Customer Name</label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="customer_phone" class="form-label">Customer Phone</label>
                            <input type="text" class="form-control" id="customer_phone" name="customer_phone" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="customer_location" class="form-label">Customer Location</label>
                            <input type="text" class="form-control" id="customer_location" name="customer_location"
                                required>
                        </div>

                        <div class="col-md-6">
                            <label for="amount" class="form-label">Amount</label>
                            <input type="number" step="0.01" class="form-control" id="amount" name="amount"
                                required>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="vin_no" class="form-label">VIN No.</label>
                            <input type="text" class="form-control" id="vin_no" name="vin_no">
                        </div>

                        <div class="col-md-6">
                            <label for="reg_no" class="form-label">Reg No.</label>
                            <input type="text" class="form-control" id="reg_no" name="reg_no">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="tin" class="form-label">TIN No.</label>
                            <input type="text" class="form-control" id="tin" name="tin">
                        </div>

                        <div class="col-md-6">
                            <label for="usage" class="form-label">Usage</label>
                            <input type="text" class="form-control" id="usage" name="usage">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="sales_executive" class="form-label">Sales Executive</label>
                            <input type="text" class="form-control" id="sales_executive" name="sales_executive">
                        </div>

                        <div class="col-md-6">
                            <label for="lead_source" class="form-label">Lead Source</label>
                            <input type="text" class="form-control" id="lead_source" name="lead_source">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="financier" class="form-label">Financier</label>
                            <input type="text" class="form-control" id="financier" name="financier">
                        </div>

                        <div class="col-md-6">
                            <label for="receipt" class="form-label">Upload Receipt</label>
                            <input type="file" class="form-control" id="receipt" name="receipt" required
                                accept="image/*,.pdf">
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">Submit</button>
                </form>
            </div>
        </div>
    </div>
@endsection
