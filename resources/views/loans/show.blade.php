@extends('layouts/contentNavbarLayout')

@section('icon', 'menu-icon tf-icons bx bx-money-withdraw')
@section('title', 'Loans - Details')
@section('content')
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                Loan Contract: <span class="text-info">{{ $loan->Status }}</span> <span
                    class="text-success">[{{ $loan->product->type->Name }}]</span>
            </h5>

            <div class="d-flex justify-content-end">
                <div class="dropdown">
                    <button class="btn btn-outline-dark btn-sm dropdown-toggle" type="button"
                            data-bs-toggle="dropdown" aria-expanded="false">
                        Actions
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end dropdown-menu-lg-start">
                        <li>
                            <a href="{{ route('loan-accounts.ledger', $loan) }}" class="dropdown-item">
                                <i class="bx bx-receipt"></i> &nbsp;View Ledger
                            </a>
                        </li>
                        <li>
                            <a href="{{ route('loan-accounts.paymentVelocity', $loan) }}" class="dropdown-item">
                                <i class="bx bx-receipt"></i> &nbsp;View Payment Velocity
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <button href="javascript:void(0);"
                                    data-bs-toggle="modal"
                                    data-bs-target="#confirmWriteOff"
                                    class="dropdown-item"
                                    type="button" {{ $loan->canWriteOff() ? '' : 'disabled' }}><i class="bx bx-edit-alt"></i> &nbsp;Write Off</button>
                        </li>
                        <li>
                            <button class="dropdown-item" type="button" data-bs-toggle="modal"
                                    data-bs-target="#confirmRestructure" {{ $loan->canBeRestructured() ? '' : 'disabled' }}>
                                <span>
                                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                         viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                         stroke-linecap="round" stroke-linejoin="round"
                                         class="lucide lucide-calendar-sync">
                                        <path d="M11 10v4h4" />
                                        <path d="m11 14 1.535-1.605a5 5 0 0 1 8 1.5" />
                                        <path d="M16 2v4" />
                                        <path d="m21 18-1.535 1.605a5 5 0 0 1-8-1.5" />
                                        <path d="M21 22v-4h-4" />
                                        <path d="M21 8.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h4.3" />
                                        <path d="M3 10h4" />
                                        <path d="M8 2v4" />
                                    </svg>
                                </span>
                                <span>Enable Restructure</span>
                            </button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <hr class="mb-0 mt-0">
        <div class="card-body">
            <h6>Loan Details</h6>
            <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Loan Product</strong>
                    <div>
                        {{ $loan->loan_product->Name }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Loan Amount</strong>
                    <div>
                        <x-money :value="$loan->Credit_Amount" />
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Facility Amount Granted</strong>
                    <div>
                        <x-money :value="$loan->Facility_Amount_Granted" />
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Interest Rate</strong>
                    <div>
                        {{ $loan->Annual_Interest_Rate_at_Disbursement }}%
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Interest Cycle</strong>
                    <div>
                        {{ $loan->loan_term->Interest_Cycle }}
                    </div>
                </li>
                {{-- <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Interest Amount</strong>
                    <div>
                        <x-money :value="$loan->totalInterest()" />
                    </div>
                </li> --}}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Maturity Date</strong>
                    <div>
                        {{ \Carbon\Carbon::parse($loan->Maturity_Date)->format('d M, Y') }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Number Of Payments</strong>
                    <div>
                        {{ $loan->Number_of_Payments }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Total Outstanding balance</strong>
                    <div>
                        <x-money :value="$loan->totalOutstandingBalance()" />
                    </div>
                </li>
            </ul>

            <h6 class="mt-4">Customer Details</h6>
            <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Full Name</strong>
                    <div>
                        {{ $loan->customer->First_Name }} {{ $loan->customer->Last_Name }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Gender</strong>
                    <div>
                        {{ $loan->customer->Gender }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Date of Birth</strong>

                    <div>
                        {{ \Carbon\Carbon::parse($loan->customer->Date_of_Birth)->format('d M, Y') }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>ID Number</strong>
                    <div>
                        {{ $loan->customer->ID_Number }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Telephone</strong>
                    <div>
                        {{ $loan->customer->Telephone_Number }}
                    </div>
                </li>
            </ul>

            <h6 class="mt-4">Application Details</h6>
            <ul class="list-group">
                {{-- <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Application Reference</strong>
                    <div>
                        {{ $loan->loan_application->Credit_Application_Reference }}
    </div>
    </li> --}}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Application Date</strong>

                    <div>
                        {{ \Carbon\Carbon::parse($loan->loan_application->Credit_Application_Date)->format('d M, Y') }}
                    </div>
                </li>
                {{-- <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Application Status</strong>
                    <div>
                        {{ $loan->loan_application->Credit_Application_Status }}
</div>
</li> --}}
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Purpose of Loan</strong>
                    <div>
                        {{ $loan->loan_application->Loan_Purpose }}
                    </div>
                </li>
            </ul>
        </div>
    </div>

    @if ($loan->isAssetLoan())

        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-info">
                    Add-Ons <span class="text-muted">(if any)</span>
                </h5>
            </div>
            <div class="card-body">
                @if ($loan->addons->count() == 0)
                    <p>No loan add-ons</p>
                @else
                    @foreach ($loan->addons as $addon)
                        <ul class="list-group mb-4">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>Type</strong>
                                <div>
                                    <strong>{{ $addon->type }}</strong>
                                </div>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>Computed On</strong>
                                <div>
                                    <x-money :value="$addon->total_principal" />
                                </div>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>Total Payment At End Of Tenure/Term</strong>
                                <div>
                                    <x-money :value="$addon->total_payment" />
                                </div>
                            </li>
                        </ul>
                    @endforeach
                @endif
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-info">
                    Asset Loan Downpayment <span class="text-muted">(if any)</span>
                </h5>
            </div>
            <div class="card-body">
                <ul class="list-group mb-4">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Total Amount</strong>
                        <div>
                            <x-money :value="$loan->downpayment?->Amount" />
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Transaction Status</strong>
                        <div>
                            <strong>{{ $loan->downpayment?->Status }}</strong>
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Asset Disbursement Status</strong>
                        <div>
                            <strong>{{ $loan->downpayment?->Asset_Disbursement_Status }}</strong>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

    @endif

    @if ($loan->Credit_Application_Status != 'Rejected')
        <livewire:loan-schedule :loan="$loan" />
    @endif

    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                Loan Repayments + (Fees & Penalties)
            </h5>
        </div>
        <hr class="mb-0 mt-0">
        {{-- <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th class="text-end">Amount</th>
                            <th class="text-end">Loan Balance After</th>
                            <th class="text-end">Transaction Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ($loan->payments->isEmpty())
                            <tr>
                                <td colspan="4" class="text-center">No loan repayment made yet.</td>
                            </tr>
                        @else
                            @foreach ($loan->payments as $payment)
                                <tr>
                                    <td>{{ $payment->id }}</td>
                                    <td class="text-end">{{ number_format($payment->amount, 2) }}</td>
                                    <td class="text-end">{{ number_format($payment->Current_Balance_Amount, 2) }}</td>
                                    <td class="text-end">
                                        {{ \Carbon\Carbon::parse($payment->Transaction_Date)->format('d M, Y') }}</td>
                                </tr>
                            @endforeach
                            <tr>
                                <td>
                                    <strong>Total</strong>
                                </td>
                                <td class="text-end">
                                    <strong>
                                        {{ number_format($loan->payments->sum('amount'), 2) }}
                                    </strong>
                                </td>
                                <td colspan="2"></td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div> --}}
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th>Category</th>
                            <th>Transaction Date</th>
                            <th class="text-end">Amount</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ($loan->payments->isEmpty())
                            <tr>
                                <td colspan="4" class="text-center">No loan repayment made yet.</td>
                            </tr>
                        @else
                            <tr>
                                <td>Principal Paid</td>
                                <td class="text">
                                    {{ \Carbon\Carbon::parse($loan->payments->first()->Transaction_Date)->format('d M, Y') }}
                                </td>
                                <td class="text-end">{{ number_format($loan->totalPrincipalPaid()) }}</td>
                            </tr>
                            <tr>
                                <td>Interest Paid</td>
                                <td class="text">
                                    {{ \Carbon\Carbon::parse($loan->payments->first()->Transaction_Date)->format('d M, Y') }}
                                </td>
                                <td class="text-end">{{ number_format($loan->totalInterestPaid()) }}</td>
                            </tr>
                            <tr>
                                <td>Fees Paid</td>
                                <td class="text">
                                    {{ \Carbon\Carbon::parse($loan->payments->first()->Transaction_Date)->format('d M, Y') }}
                                </td>
                                <td class="text-end">{{ number_format(round($loan->fees()->sum('Amount'))) }}</td>
                            </tr>
                            <tr>
                                <td>Penalties Paid</td>
                                <td class="text">
                                    {{ \Carbon\Carbon::parse($loan->payments->first()->Transaction_Date)->format('d M, Y') }}
                                </td>
                                <td class="text-end">{{ number_format(round($loan->penalties()->sum('Amount'))) }}</td>
                            </tr>
                            <tr>
                                <td>
                                </td>
                                <td><strong>Total</strong>
                                </td>
                                <td class="text-end">
                                    <strong>
                                        {{ number_format($loan->payments->sum('amount'), 2) }}
                                    </strong>
                                </td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                Loan Fees <span class="text-muted">(if any)</span>
            </h5>
        </div>
        <div class="card-body">
            @if ($loan->fees->count() == 0)
                <p>No loan fees</p>
            @endif
            @foreach ($loan->fees as $fee)
                <ul class="list-group mb-4">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Name</strong>
                        <div>
                            {{ $fee->loan_product_fee->Name }}
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Amount</strong>
                        <div>
                            <x-money :value="$fee->Amount_To_Pay" />
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Applicable At</strong>
                        <div>
                            {{ $fee->Charge_At }}
                        </div>
                    </li>
                </ul>
            @endforeach
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                Loan Penalties <span class="text-muted">(if any)</span>
            </h5>
        </div>
        <hr class="mb-0 mt-0">
        <div class="card-body">
            @if ($loan->penalties->count() == 0)
                <p>No loan penalties</p>
            @endif
            @foreach ($loan->penalties as $fee)
                <ul class="list-group mb-4">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Amount To Pay</strong>
                        <div>
                            <x-money :value="$fee->Amount_To_Pay" />
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Amount Paid</strong>
                        <div>
                            <x-money :value="$fee->Amount" />
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Payment Status</strong>
                        <div>
                            {{ $fee->Status }}
                        </div>
                    </li>
                </ul>
            @endforeach
        </div>
    </div>

    <div>
        <!-- Disable Modal -->
        <div class="modal fade" id="confirmWriteOff" data-bs-backdrop="static" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="confirmWriteOffTitle">Confirm action
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Are you sure you want to Write-Off this Loan?</p>
                        <span class="text-danger">This action cannot be undone.</span>
                        <div class="mt-4">
                            <label class="form-label">Reason for Write-off</label>
                            <input type="text" class="form-control" name="reason" required maxlength="255"
                                form="write-off-form">
                        </div>
                        <div class="mt-4">
                            <label class="form-label">Write-off Date</label>
                            <input type="date" class="form-control" name="write_off_date" required
                                value="{{ date('Y-m-d') }}" form="write-off-form">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <form action="{{ route('loan-accounts.writeOff', $loan->id) }}" method="POST"
                            id="write-off-form">
                            @csrf
                            @method('PUT')
                            <button type="button" class="btn btn-outline-secondary"
                                data-bs-dismiss="modal">Cancel</button>
                            &nbsp;&nbsp;
                            <button type="submit" class="btn btn-danger">Confirm Action</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
