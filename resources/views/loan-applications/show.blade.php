@extends('layouts.contentNavbarLayout')
@section('icon', 'menu-icon tf-icons bx bx-money-withdraw')
@section('title', 'Loan Application - Appraisal')
@section('content')

    <div class="card">
        <div class="card-body">
            <h5 class="">Customer Details</h5>
            <ul class="list-group">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Full Name:</strong>
                    <div>
                        {{ $application->customer->First_Name }} {{ $application->customer->Last_Name }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Gender:</strong>
                    <div>
                        {{ $application->customer->Gender }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Date of Birth:</strong>

                    <div>{{ $application->customer->Date_of_Birth }}</div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>ID Number:</strong>
                    <div>
                        {{ $application->customer->ID_Number }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Telephone:</strong>
                    <div>
                        {{ $application->customer->Telephone_Number }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Score:</strong>
                    <div>
                        {{ $ussdHit->score ?? '' }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Risk Band:</strong>
                    <div>
                        {{ $ussdHit->score_band ?? '' }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Monthly Turnover:</strong>
                    <div>
                        {{ $ussdHit->monthly_turnover ?? '' }}
                    </div>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                    <strong>Phone Count:</strong>
                    <div>
                        {{ $ussdHit->phone_count ?? '' }}
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <!-- check if customer hasCreditScore -->
    @if ($application->customer->getCreditScores()->count() > 0)
        <div class="card mt-4">
            <div class="card-body">
                <h5>Scoring Details</h5>
                <!-- draw a table of all customer credit scores below -->
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>MNO Months Active</th>
                            <th>MNO Rating</th>
                            <th>MNO 6 Months Turnover </th>
                            <th>MNO Active Loans </th>
                            <th>MNO Rating </th>
                            <th>CRB Active Loans </th>
                            <th>CRB Rating </th>
                            <th>Other Active Loans </th>
                            <th>Other Provider Rating</th>
                            <th>Created At</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach ($application->customer->getCreditScores()->get() as $score)
                            <tr>
                                <td>{{ $score->mnoMonthsActive }}</td>
                                <td>{{ $score->mnoRating }}</td>
                                <td>{{ $score->mnoMonthlyTurnoverAmount6Months }}</td>
                                <td>{{ $score->mnoAccounts12Months }}</td> <!-- change this line later to open accounts -->
                                <td>{{ $score->mnoRating }}</td>
                                <td>{{ $score->crbOpenAccounts }}</td>
                                <td>{{ $score->crbRating }}</td>
                                <td>{{ $score->altOpenAccounts }}</td>
                                <td>{{ $score->altRating }}</td>
                                <td>{{ $score->created_at }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif
    @if ($application->loan_product->isAssetloan())
        <div class="card mt-4">
            <div class="card-body">
                <h5>Down Payment Details</h5>

                @if (!$application->loan_downpayment)
                    <p>No down payment details</p>
                @else
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Amount</strong>
                            <div>
                                {{ $application->loan_downpayment->amount }}
                            </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Transaction ID</strong>
                            <div>
                                {{ $application->downPayment->TXN_ID }}
                            </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Transaction Status</strong>
                            <div>
                                {{ $application->downPayment->Status }}
                            </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Documents Status</strong>
                            <div>
                                {{ $application->downPayment->Asset_Disbursement_Status }}
                            </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Transaction Date</strong>
                            <div>
                                {{ $application->downPayment->created_at }}
                            </div>
                        </li>
                        @if (!$application->loan && $application->downPayment->Asset_Disbursement_Status == 'Submitted')
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                <strong>Action</strong>
                                <div>
                                    <a class="btn btn-sm btn-dark" href="javascript:void(0);" data-bs-toggle="modal"
                                        data-bs-target="#disburseLoanModal{{ $application->downPayment->TXN_ID }}">
                                        <span>
                                            Approve Loan
                                        </span>
                                    </a>
                                    <a class="btn btn-sm btn-danger" href="javascript:void(0);" data-bs-toggle="modal"
                                        data-bs-target="#rejectLoanModal{{ $application->id }}">
                                        <span class="">
                                            {{-- <i class="bx bx-edit"></i> &nbsp; --}}
                                            Reject
                                        </span>
                                    </a>
                                    @include('loan-applications.partials.disburse-loan-modal')
                                    @include('loan-applications.partials.reject-loan-modal')
                                </div>
                            </li>
                        @endif
                    </ul>

                    @if (!empty($customerFiles))
                        <div class="mt-4">
                            <div class="d-flex justify-content-start fs-6 mb-4">
                                <span>Files Uploaded: {{ count($customerFiles) }}</span>
                            </div>
                            <ul class="list-group mb-5">
                                @foreach ($customerFiles as $customerFile)
                                    <li
                                        class="list-group-item border-0 d-flex justify-content-between align-items-center mb-2 shadow-sm">
                                        <div class="d-flex align-items-center">
                                            <span class="me-2 text-secondary">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="lucide lucide-paperclip">
                                                    <path d="M13.234 20.252 21 12.3" />
                                                    <path
                                                        d="m16 6-8.414 8.586a2 2 0 0 0 0 2.828 2 2 0 0 0 2.828 0l8.414-8.586a4 4 0 0 0 0-5.656 4 4 0 0 0-5.656 0l-8.415 8.585a6 6 0 1 0 8.486 8.486" />
                                                </svg>
                                            </span>
                                            <div class="d-flex justify-content-between">
                                                {{ str($customerFile)->after($application->id . '/')->toString() }}
                                            </div>
                                        </div>
                                        <a href="{{ route('loan-applications.download', [$application, 'file' => $customerFile]) }}"
                                            class="btn btn-sm text-dark">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="lucide lucide-arrow-down-to-line">
                                                <path d="M12 17V3" />
                                                <path d="m6 11 6 6 6-6" />
                                                <path d="M19 21H5" />
                                            </svg>
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    @endif
                @endif
            </div>
        </div>
    @endif
    <div class="card mt-4">
        <div class="card-body">
            <h5 class="mt-4">Loan Details</h5>

            @if (!$application->loan)
                <p>No loan details</p>
            @else
                <ul class="list-group">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Loan Duration:</strong>
                        <div>
                            {{ $application->loan->Term }}
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Daily Repayment Amount:</strong>
                        <div>
                            {{ $application->loan->getDailyRepayment() }}
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Total Repayment Amount:</strong>
                        <div>
                            {{ $application->loan->totalToBePaid() }}
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Total Fees</strong>
                        <div>
                            {{ $application->loan->totalFees() }}
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Repayment Frequency</strong>
                        <div>
                            {{ $application->loan->Credit_Payment_Frequency }}
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Annual Interest Rate:</strong>
                        <div>
                            {{ $application->loan->Interest_Rate }}
                        </div>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <strong>Interest Calculation Method</strong>
                        <div>
                            {{ $application->loan->Interest_Calculation_Method }}
                        </div>
                    </li>
                </ul>
            @endif
        </div>
    </div>
    <div class="card mt-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                Loan Fees <span class="text-muted"></span>
            </h5>
        </div>
        <div class="card-body">
            @if ($application->loan && $application->loan->fees->isEmpty())
                <p>No loan fees</p>
            @elseif ($application->loan && $application->loan->fees)
                @foreach ($application->loan->fees as $fee)
                    <ul class="list-group mb-4">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Name:</strong>
                            <div>
                                {{ $fee->loan_product_fee->Name }}
                            </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Amount:</strong>
                            <div>
                                {{ $fee->Amount_To_Pay }}
                            </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Applicable At:</strong>
                            <div>
                                {{ $fee->Charge_At }}
                            </div>
                        </li>
                    </ul>
                @endforeach
            @endif

        </div>
    </div>
    @if ($application->loan && $application->loan->addons->isEmpty())
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    Add-Ons <span class="text-muted"></span>
                </h5>
            </div>
            <div class="card-body">
                <p>No loan addons</p>
            </div>
        </div>
    @elseif ($application->loan && $application->loan->addons)
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    Add-Ons <span class="text-muted"></span>
                </h5>
            </div>
            <div class="card-body">
                @foreach ($application->loan->addons as $addon)
                    <ul class="list-group mb-4">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Type:</strong>
                            <div> {{ $addon->type }} </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Principal:</strong>
                            <div> {{ $addon->total_principal }} </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Tenure:</strong>
                            <div> {{ $addon->total_interest }} </div>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <strong>Interest:</strong>
                            <div> {{ $addon->total_payment }} </div>
                        </li>
                    </ul>
                @endforeach
            </div>
        </div>
    @endif
@endsection
