<table>
    <thead>
        <tr>
            <th colspan="10" style="font-weight: bold; font-size: 16px; text-align: center;">
                {{ $partnerName }}
            </th>
        </tr>
        <tr>
            <th colspan="10" style="font-weight: bold; font-size: 14px; text-align: center;">
                Payment History Velocity Report
            </th>
        </tr>
        <tr>
            <th colspan="10" style="font-size: 12px; text-align: center;">
                Period: {{ $filters['startDate'] }} to {{ $filters['endDate'] }}
            </th>
        </tr>
        <tr></tr>
        <tr>
            <th class="text-start" colspan="5">Loan Information</th>
            <th class="text-start" colspan="5">Loan Summary</th>
        </tr>
        <tr>
            <td colspan="5">
                <div class="table">
                    <div>
                        <strong>Account Reference:</strong>
                        <span>{{ $loan->Credit_Account_Reference }}</span>
                    </div>
                    <div>
                        <strong>Customer:</strong>
                        <span>{{ $loan->customer->name }}</span>
                    </div>
                    <div>
                        <strong>Phone:</strong>
                        <span>{{ $loan->customer->Telephone_Number }}</span>
                    </div>
                    <div>
                        <strong>Loan Product:</strong>
                        <span>{{ $loan->loan_product->Name ?? 'N/A' }}</span>
                    </div>
                </div>
            </td>
            <td colspan="5">
                <table class="table">
                    <tr>
                        <td>Principal Amount:</td>
                        <td>{{ number_format($loan->Credit_Amount, 2) }} {{ $loan->Currency }}</td>
                    </tr>
                    <tr>
                        <td>Disbursement Date:</td>
                        <td>{{ $loan->Credit_Account_Date?->format('d M Y') ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td>Maturity Date:</td>
                        <td>{{ $loan->Maturity_Date?->format('d M Y') ?? 'N/A' }}</td>
                    </tr>
                    <tr>
                        <td>Status:</td>
                        <td>
                            {{ \App\Enums\LoanAccountType::formattedName($loan->Credit_Account_Status) }}
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <!-- Data Headers -->
        <tr>
            <th class="text-start">Type</th>
            <th class="text-start">Installment #</th>
            <th class="text-end">Principal</th>
            <th class="text-end">Interest</th>
            <th class="text-end">Due Date</th>
            <th class="text-end">Payment Date</th>
            <th class="text-end">Days Difference</th>
            <th class="text-start">Indicator</th>
            <th class="text-end">Installment Amount</th>
            <th class="text-end">Payment Amount</th>
        </tr>
    </thead>
    <tbody>
    @forelse($records as $record)
        <tr>
            <td>{{ $record->type }}</td>
            <td>{{ $record->installment_number }}</td>
            <td class="text-end">{{ $record->principal }}</td>
            <td class="text-end">{{ $record->interest }}</td>
            <td class="text-end">{{ \Carbon\Carbon::parse($record->payment_due_date)->format('d-m-Y') }}</td>
            <td class="text-end">{{ \Carbon\Carbon::parse($record->payment_date)->format('d-m-Y') }}</td>
            <td class="text-end">{{ $record->days_difference }}</td>
            <td>{{ $record->days_difference < 0 ? 'Early' : ($record->days_difference == 0 ? 'On Time' : 'Late') }}</td>
            <td class="text-end">{{ $record->installment_amount }}</td>
            <td class="text-end">{{ $record->payment_amount }}</td>
        </tr>
    @empty
        <tr>
            <td colspan="10" class="text-center">No records found</td>
        </tr>
    @endforelse
    </tbody>
</table>

<table style="margin-top: 20px;">
    <tr>
        <th colspan="10" style="font-weight: bold; background-color: #f5f5f5;">Understanding Payment History Velocity</th>
    </tr>
    <tr>
        <td colspan="10">Payment History Velocity tracks changes in payment timing patterns, which can be an early indicator of potential default risk.</td>
    </tr>
    <tr>
        <td colspan="10"><strong>Early Payments:</strong> Payments made before the due date (negative days difference)</td>
    </tr>
    <tr>
        <td colspan="10"><strong>On Time Payments:</strong> Payments made exactly on the due date (0 days difference)</td>
    </tr>
    <tr>
        <td colspan="10"><strong>Late Payments:</strong> Payments made after the due date (positive days difference)</td>
    </tr>
    <tr>
        <td colspan="10"><strong>Risk Indicator:</strong> A shift from early/on-time payments to late payments may indicate financial stress and increased default risk.</td>
    </tr>
</table>
