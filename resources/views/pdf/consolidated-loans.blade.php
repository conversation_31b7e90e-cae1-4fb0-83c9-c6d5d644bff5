@extends('pdf.layouts')

@section('content')
    <div class="text-center">
        <h2 style="margin-bottom: 5px; margin-top: 0; font-size: 16px">{{ $partnerName }}</h2>
        <h4 style="margin-top: 0; margin-bottom: 4px">Consolidated Loans Report</h4>
        <p style="margin-top: 0; font-size: 10px">From: {{ $filters['startDate'] }} to {{ $filters['endDate'] }}</p>
    </div>

    <table class="table table-bordered" id="report-table">
        <thead>
            <tr class="table-header">
                <th class="text-end">Loan#</th>
                <th class="text-start">Customer Name</th>
                <th class="text-start">Phone Number</th>
                <th class="text-end">Amount Disbursed</th>
                <th class="text-end">Date Disbursed</th>
                <th class="text-end">Maturity Date</th>
                <th class="text-end">Outstanding Balance</th>
                <th class="text-end">Interest Rate</th>
                <th class="text-end">Interest Receivable</th>
                <th class="text-end">Arrears Days</th>
                <th class="text-end">Arrears Amount</th>
                <th class="text-end">Principal Overdue</th>
                <th class="text-end">Interest Overdue</th>
                <th class="text-end">Penal Interest Overdue</th>
                <th class="text-end">Last Payment</th>
                <th>Gender</th>
            </tr>
        </thead>
        <tbody>
            @forelse($records as $record)
                <tr>
                    <td class="text-end">{{ $record->id }}</td>
                    <td>{{ $record->customer->name }}</td>
                    <td>{{ $record->customer->Telephone_Number }}</td>
                    <td class="text-end">{{ number_format($record->Facility_Amount_Granted) }}</td>
                    <td class="text-end">{{ $record->Credit_Account_Date->toDateString() }}</td>
                    <td class="text-end">{{ $record->Maturity_Date->toDateString() }}</td>
                    <td class="text-end">{{ number_format($record->schedule_sum_total_outstanding) }}</td>
                    <td class="text-end">{{ $record->Annual_Interest_Rate_at_Disbursement }}%</td>
                    <td class="text-end">{{ number_format($record->schedule_sum_interest) }}</td>
                    <td class="text-end">{{ number_format(abs($record->arrears_days)) }}</td>
                    <td class="text-end">{{ number_format($record->total_arrears) }}</td>
                    <td class="text-end">{{ number_format($record->total_principal_overdue) }}</td>
                    <td class="text-end">{{ number_format($record->total_interest_overdue) }}</td>
                    <td class="text-end">{{ number_format($record->penalties_overdue) }}</td>
                    <td class="text-end">{{ $record->last_payment_date?->toDateString() }}</td>
                    <td>{{ $record->customer->Gender }}</td>
                </tr>
            @empty
                <tr>
                    <td colspan="16" class="text-center">No records found</td>
                </tr>
            @endforelse
        </tbody>
        <tfoot>
            <tr>
                <th>Totals</th>
                <th class="text-end">{{ $records->count() }}</th>
                <th></th>
                <th class="text-end">{{ number_format($records->sum('Facility_Amount_Granted')) }}</th>
                <th colspan="2"></th>
                <th class="text-end">{{ number_format($records->sum('schedule_sum_total_outstanding')) }}</th>
                <th></th>
                <th class="text-end">{{ number_format($records->sum('schedule_sum_interest')) }}</th>
                <th></th>
                <th class="text-end">{{ number_format($records->sum('total_arrears')) }}</th>
                <th class="text-end">{{ number_format($records->sum('total_principal_overdue')) }}</th>
                <th class="text-end">{{ number_format($records->sum('total_interest_overdue')) }}</th>
                <th class="text-end">{{ number_format($records->sum('penalties_overdue')) }}</th>
                <th colspan="2"></th>
            </tr>
        </tfoot>
    </table>
    <x-print-footer />
@endsection
