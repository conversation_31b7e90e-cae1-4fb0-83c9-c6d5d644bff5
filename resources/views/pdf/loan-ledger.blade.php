<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><PERSON>an <PERSON> - {{ $loan->Credit_Account_Reference }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .company-name {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .report-date {
            font-size: 10px;
            color: #666;
        }
        .loan-info {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        .loan-info-left, .loan-info-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .loan-info-right {
            padding-left: 20px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
        }
        .info-table td {
            padding: 3px 0;
            border: none;
        }
        .info-table td:first-child {
            font-weight: bold;
            width: 40%;
        }
        .ledger-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .ledger-table th,
        .ledger-table td {
            border: 1px solid #ddd;
            padding: 6px;
            text-align: left;
        }
        .ledger-table th {
            background-color: #f5f5f5;
            font-weight: bold;
            font-size: 11px;
        }
        .ledger-table td {
            font-size: 10px;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .debit {
            color: #d32f2f;
        }
        .credit {
            color: #388e3c;
        }
        .balance {
            font-weight: bold;
        }
        .transaction-type {
            font-size: 9px;
            padding: 2px 4px;
            border-radius: 2px;
            color: white;
        }
        .type-disbursement { background-color: #2196f3; }
        .type-repayment { background-color: #4caf50; }
        .type-penalty { background-color: #ff9800; }
        .type-fee { background-color: #9c27b0; }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .summary-box {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ $partnerName }}</div>
        <div class="report-title">LOAN LEDGER REPORT</div>
        <div class="report-date">
            Generated on {{ now()->format('d M Y H:i:s') }}
            @if(isset($filters['startDate']) && $filters['startDate'])
                | Period: {{ \Carbon\Carbon::parse($filters['startDate'])->format('d M Y') }} - {{ \Carbon\Carbon::parse($filters['endDate'])->format('d M Y') }}
            @endif
        </div>
    </div>

    <!-- Loan Information -->
    <div class="loan-info">
        <div class="loan-info-left">
            <table class="info-table">
                <tr>
                    <td>Account Reference:</td>
                    <td>{{ $loan->Credit_Account_Reference }}</td>
                </tr>
                <tr>
                    <td>Customer Name:</td>
                    <td>{{ $loan->customer->name }}</td>
                </tr>
                <tr>
                    <td>Phone Number:</td>
                    <td>{{ $loan->customer->Telephone_Number }}</td>
                </tr>
                <tr>
                    <td>Loan Product:</td>
                    <td>{{ $loan->loan_product->name ?? 'N/A' }}</td>
                </tr>
            </table>
        </div>
        <div class="loan-info-right">
            <table class="info-table">
                <tr>
                    <td>Principal Amount:</td>
                    <td>{{ number_format($loan->Credit_Amount, 2) }} {{ $loan->Currency }}</td>
                </tr>
                <tr>
                    <td>Disbursement Date:</td>
                    <td>{{ $loan->Credit_Account_Date?->format('d M Y') ?? 'N/A' }}</td>
                </tr>
                <tr>
                    <td>Maturity Date:</td>
                    <td>{{ $loan->Maturity_Date?->format('d M Y') ?? 'N/A' }}</td>
                </tr>
                <tr>
                    <td>Current Status:</td>
                    <td>{{ \App\Models\Loan::SUPPORTED_Credit_Account_Statuses[$loan->Credit_Account_Status] ?? 'Unknown' }}</td>
                </tr>
            </table>
        </div>
    </div>

    <!-- Transaction Summary -->
    <div class="summary-box">
        <strong>Transaction Summary:</strong>
        Total Debits: {{ number_format($records->sum('debit_amount'), 2) }} {{ $loan->Currency }} |
        Total Credits: {{ number_format($records->sum('credit_amount'), 2) }} {{ $loan->Currency }} |
        Net Balance: {{ number_format($records->sum('debit_amount') - $records->sum('credit_amount'), 2) }} {{ $loan->Currency }}
    </div>

    <!-- Ledger Table -->
    @if($records->count() > 0)
        <table class="ledger-table">
            <thead>
                <tr>
                    <th width="12%">Date</th>
                    <th width="15%">Transaction Type</th>
                    <th width="25%">Description</th>
                    <th width="15%">Account</th>
                    <th width="11%" class="text-right">Debit</th>
                    <th width="11%" class="text-right">Credit</th>
                    <th width="11%" class="text-right">Balance</th>
                </tr>
            </thead>
            <tbody>
                @php $runningBalance = 0; @endphp
                @foreach($records as $entry)
                    @php
                        // Calculate running balance
                        if ($entry->accounting_type === 'debit') {
                            $runningBalance += $entry->debit_amount ?? $entry->amount;
                        } else {
                            $runningBalance -= $entry->credit_amount ?? $entry->amount;
                        }
                        
                        $transactionType = match($entry->transactable) {
                            'App\Models\LoanDisbursement' => 'Disbursement',
                            'App\Models\LoanRepayment' => 'Repayment',
                            'App\Models\LoanPenalty' => 'Penalty',
                            'App\Models\LoanFee' => 'Fee',
                            default => 'Other'
                        };
                    @endphp
                    <tr>
                        <td>{{ $entry->created_at->format('d M Y H:i') }}</td>
                        <td>{{ $transactionType }}</td>
                        <td>
                            {{ $entry->account_name }}
                            @if($entry->journable)
                                <br><small style="color: #666;">
                                    @if($entry->transactable === 'App\Models\LoanDisbursement')
                                        Amount: {{ number_format($entry->journable->amount ?? 0, 2) }}
                                    @elseif($entry->transactable === 'App\Models\LoanRepayment')
                                        Payment: {{ number_format($entry->journable->amount ?? 0, 2) }}
                                    @endif
                                </small>
                            @endif
                        </td>
                        <td>{{ $entry->account->name ?? 'N/A' }}</td>
                        <td class="text-right debit">
                            {{ $entry->debit_amount > 0 ? number_format($entry->debit_amount, 2) : '-' }}
                        </td>
                        <td class="text-right credit">
                            {{ $entry->credit_amount > 0 ? number_format($entry->credit_amount, 2) : '-' }}
                        </td>
                        <td class="text-right balance">
                            {{ number_format(abs($runningBalance), 2) }}{{ $runningBalance < 0 ? ' (CR)' : '' }}
                        </td>
                    </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr style="background-color: #f0f0f0; font-weight: bold;">
                    <td colspan="4">TOTALS</td>
                    <td class="text-right debit">{{ number_format($records->sum('debit_amount'), 2) }}</td>
                    <td class="text-right credit">{{ number_format($records->sum('credit_amount'), 2) }}</td>
                    <td class="text-right balance">
                        {{ number_format(abs($runningBalance), 2) }}{{ $runningBalance < 0 ? ' (CR)' : '' }}
                    </td>
                </tr>
            </tfoot>
        </table>
    @else
        <div class="text-center" style="padding: 40px;">
            <p>No transactions found for this loan account.</p>
        </div>
    @endif

    <!-- Footer -->
    <div class="footer">
        <p>This is a computer-generated report. No signature required.</p>
        <p>{{ $partnerName }} - Loan Management System</p>
    </div>
</body>
</html>
