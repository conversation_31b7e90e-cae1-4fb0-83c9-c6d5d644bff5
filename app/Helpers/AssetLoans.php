<?php

namespace App\Helpers;

use Exception;
use Carbon\Carbon;
use App\Models\Loan;
use App\Models\SmsLog;
use App\Models\LoanRepayment;
use App\Models\LoanDownpayment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\LmsUssdSessionTracking;
use App\Models\LoanFee;
use App\Models\Transaction;
use App\Notifications\SmsNotification;
use Illuminate\Support\Facades\Artisan;

class AssetLoans
{
    public static function affectDownpaymentAccounts($loan_application, $down_payment_amount)
    {
        DB::beginTransaction();
        try {
            $loan_downpayment = LoanDownpayment::create([
                'customer_id' => $loan_application->Customer_ID,
                'partner_id' => $loan_application->Partner_ID,
                'loan_application_id' => $loan_application->id,
                'amount' => $down_payment_amount,
            ]);
            $loan_downpayment->affectAccounts();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
        }
    }

    public static function affectDisbursementAccounts($loan_application)
    {
        DB::beginTransaction();
        try {
            $loanApplicationSession = LmsUssdSessionTracking::query()
                ->where('Loan_Application_ID', $loan_application->id)
                ->firstOrFail();

            $loanProductTerm = $loanApplicationSession->loanProductTerm;
            $loanProduct = $loanApplicationSession->loanProduct;

            $loan = Loan::create([
                'Partner_ID' => $loan_application->partner->id,
                'Customer_ID' => $loan_application->customer->id,
                'Loan_Product_ID' => $loan_application->loan_product->id,
                'Loan_Application_ID' => $loan_application->id,
                'Credit_Application_Status' => 'Approved', // Waiting for Approval from Lending Partner
                'Credit_Account_Reference' => Loan::generateReference(),
                'Credit_Account_Date' => Carbon::now(),
                'Credit_Amount' => $loan_application->Amount,
                'Facility_Amount_Granted' => $loan_application->Amount,
                'Credit_Amount_Drawdown' => '0.00', // Confirm this @Najja
                'Credit_Account_Type' => $loan_application->loan_product->loan_product_type->Code,
                'Currency' => 'UGX',
                'Maturity_Date' => $loan_application->loan_session->Maturity_Date,
                'Annual_Interest_Rate_at_Disbursement' => $loanProductTerm->Interest_Rate,
                'Date_of_First_Payment' => $loan_application->loan_session->Date_of_First_Payment,
                'Credit_Amortization_Type' => 1, // Refer to the DSM APPENDIX 1.11
                'Credit_Payment_Frequency' => $loanApplicationSession->Credit_Payment_Frequency ?? "Monthly",
                'Number_of_Payments' => $loanApplicationSession->Number_of_Payments ?? 1,
                'Client_Advice_Notice_Flag' => 'Yes',
                'Term' => $loanApplicationSession->Number_of_Payments,
                'Type_of_Interest' => 1, // Refer to the DSM APPENDIX 1.9 0-Fixed, 1-Floating
                'Client_Consent_Flag' => 'Yes',
                'Interest_Rate' => $loanProductTerm->Interest_Rate,
                'Interest_Calculation_Method' => $loanProductTerm->Interest_Calculation_Method,
                'Loan_Term_ID' => $loanProductTerm->id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            $loanApplicationSession->update([
                'Loan_ID' => $loan->id,
            ]);
            $transaction = Transaction::where('Loan_Application_ID', $loanApplicationSession->Loan_Application_ID)->latest()->first();
            $transaction->Loan_ID = $loan->id;
            $transaction->save();

            DB::commit();
            $message = 'Dear ' . $loan->customer->name . ' your ' . $loanProduct->Name . ' has been successfully disbursed. Repayment is due by ' . $loan->schedule()->min('payment_due_date') . ', Thank you.';
            // SmsLog::store($message, $loan->Partner_ID, $loan->customer->id, 'Loan Disbursement', $loan->customer->Telephone_Number);
            $customer = $loan->customer;
            $customer->notify(new SmsNotification($message, $customer->Telephone_Number, $customer->id, $loan->Partner_ID));
            if ($loan->partner->Identification_Code == 'CB009') {
                Artisan::call('lms:post-loan-to-spiro', [
                    '--loanId' => $loan->id // Replace with your loan ID
                ]);
            }
            return response()->json(['returnCode' => 200, 'returnMessage' => 'Loan approved successfully']);
        } catch (\Throwable $throwable) {
            DB::rollBack();
            Log::error($throwable->getMessage());
            Log::error($throwable->getTraceAsString());
            return response()->json(['returnCode' => 400, 'returnMessage' => $throwable->getMessage()]);
        }
    }

    public static function affectRepaymentAccounts($loan, $amount)
    {
        DB::beginTransaction();
        try {
            if (!$loan) {
                throw new Exception('Loan that performed the transaction was not found. Of they changed their phone number.');
            }

            $customer = $loan->customer;
            if (!$customer) {
                throw new Exception('Customer that performed the transaction was not found. Of they changed their phone number.');
            }

            $loan_payment = LoanRepayment::createPayment($loan, $amount);
            $loan_payment->affectAccounts();
            DB::commit();
        } catch (\Throwable $throwable) {
            DB::rollBack();
        }
    }
}
