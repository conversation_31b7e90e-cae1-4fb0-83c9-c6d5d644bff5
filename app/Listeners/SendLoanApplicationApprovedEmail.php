<?php

namespace App\Listeners;

use App\Models\Partner;
use App\Models\Transaction;
use Illuminate\Support\Facades\Mail;
use App\Events\LoanApplicationApproved;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendLoanApplicationApprovedEmail
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(LoanApplicationApproved $event): void
    {
        $partner = $event->loanApplication->partner;
        $transaction = Transaction::query()->firstWhere('Loan_Application_ID', $event->loanApplication->id);
        // todo: Attach partner to 
        $assetPartner = Partner::query()->firstWhere('Institution_Name', 'like', '%' . $transaction?->Asset_Provider . '%');

        if ($assetPartner?->Email_Notification_Recipients) {
            $mail = Mail::to(explode(',', $assetPartner->Email_Notification_Recipients));

            if (! empty($partner->Email_Notification_Recipients)) {
                $mail->cc(explode(',', $partner->Email_Notification_Recipients));
            }

            $mail->send(new \App\Mail\LoanApplicationApproved($event->loanApplication));

            return;
        }
    }
}
