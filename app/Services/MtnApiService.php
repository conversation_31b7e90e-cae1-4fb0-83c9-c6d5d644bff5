<?php

namespace App\Services;

use App\Actions\Loans\CreateApprovedLoanAction;
use App\Models\Customer;
use App\Models\Loan;
use Carbon\CarbonInterface;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Client\PendingRequest;
use App\Exceptions\MtnApiException;
use App\Services\Contracts\ProvidesTransactableAPIs;
use App\Models\Transaction;
use App\Actions\Loans\ProcessLoanRepaymentAction;
use App\Actions\Loans\ProcessLoanRestructureAction;
use App\Actions\Loans\AffectAssetLoanDownPaymentAction;
use App\Actions\Savings\ProcessSavingsDepositAction;
use App\Actions\Savings\ProcessSavingsWithdrawalAction;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class MtnApiService implements ProvidesTransactableAPIs
{
    const STATUS_SUCCESS = "SUCCEEDED";
    const STATUS_PENDING = "PENDING";
    const STATUS_FAILED = "FAILED";

    public string $baseUrl = '';
    public string $baseContext = '';
    private bool $verifySsl = true;
    private int $timeout = 30;
    private bool $debug = false;
    private string $dfcuCaCertificate = '';
    private string $dfcuCertificate = '';
    private string $lmsPrivateKey = '';
    private string $m3ExternalCertificate = '';
    private string $loansUsername = '';
    private string $loansPassword = '';
    private string $savingsUsername = '';
    private string $savingsPassword = '';

    public function __construct(
        private readonly string $environment = 'test',
        private readonly array $config = []
    ) {
        $env = strtolower($this->environment);

        $this->baseUrl = Arr::get($this->config, 'mtn_url', config("services.mtn.{$env}.url"));
        $this->baseContext = config("services.mtn.{$env}.base_context", 'sp');
        $this->verifySsl = config("services.mtn.{$env}.verify_ssl", true);
        $this->timeout = config("services.mtn.{$env}.timeout", 30);
        $this->debug = config("services.mtn.{$env}.debug", false);
        $this->dfcuCaCertificate = config("services.mtn.{$env}.dfcu_ca_certificate", '');
        $this->dfcuCertificate = config("services.mtn.{$env}.dfcu_certificate", '');
        $this->lmsPrivateKey = config("services.mtn.{$env}.lms_private_key", '');
        $this->m3ExternalCertificate = config("services.mtn.{$env}.m3_external_certificate", '');
        $this->loansUsername = config("services.mtn.{$env}.loans_username", '');
        $this->loansPassword = config("services.mtn.{$env}.loans_password", '');
        $this->savingsUsername = config("services.mtn.{$env}.savings_username", '');
        $this->savingsPassword = config("services.mtn.{$env}.savings_password", '');

        if (! $this->isWeekend()) {
            throw new MtnApiException('Service not available on weekdays');
        }
    }

    public function isWeekend(): bool
    {
        return true;
        if (app()->isLocal()) {
            return true;
        }

        $weekend = [CarbonInterface::FRIDAY, CarbonInterface::SATURDAY, CarbonInterface::SUNDAY];

        return in_array(now()->dayOfWeek, $weekend);
    }

    public function getStatusSuccessMessage(): string
    {
        return 'SUCCEEDED';
    }

    /**
     * @throws MtnApiException
     * @throws Exception
     */
    public function disburse(
        $phone_number,
        $amount,
        $txn_id,
        $reason = "Disbursement"
    ): array {
        $this->validatePhoneNumber($phone_number);
        $this->validateAmount($amount);

        try {
            $transaction = Transaction::query()->with('loanApplication')->firstWhere('TXN_ID', $txn_id);

            // First we confirm to MTN that the loan application has been completed.
            $loanApplicationCompleted = $this->loanApplicationCompleted($transaction);

            if (! $loanApplicationCompleted) {
                throw new Exception('Loan Application Error: See application logs');
            }

            $customer = Customer::query()->firstWhere('Telephone_Number', $phone_number);

            $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
                '<p:disbursementrequest xmlns:p="http://www.ericsson.com/em/emm/sp/frontend" xmlns:op="http://www.ericsson.com/em/emm/common" xmlns:java="http://www.oracle.com/XSL/Transform/java" xmlns:tns="http://www.temenos.com/T24/event/RWFInbound/CusAsyncResp">' . PHP_EOL .
                '    <sendingfri>FRI:WKD-PERSONAL/USER</sendingfri>' . PHP_EOL .
                '    <receivingfri>FRI:' . $phone_number . '/MSISDN</receivingfri>' . PHP_EOL .
                '    <amount>'. PHP_EOL .
                '        <amount>'. $amount . '</amount>'. PHP_EOL .
                '        <currency>UGX</currency>'. PHP_EOL .
                '    </amount>'. PHP_EOL .
                '    <providertransactionid>'. $txn_id . '</providertransactionid>'. PHP_EOL .
                '    <name>'. PHP_EOL .
                '        <firstname>'. $customer->First_Name . '</firstname>'. PHP_EOL .
                '        <lastname>'. $customer->Last_Name . '</lastname>'. PHP_EOL .
                '    </name>'. PHP_EOL .
                '    <sendernote>'. $reason . '</sendernote>'. PHP_EOL .
                '    <referenceid>'. $txn_id . '</referenceid>'. PHP_EOL .
                '</p:disbursementrequest>';

            Log::info('MTN Disbursement Payload: ' . $payload);

            $response = $this->getClient()->post('/sp/disbursement', [
                'body' => $payload
            ]);

            // Process successful response
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            $result = $this->xmlToArray($body);

            if ($statusCode !== 200) {
                throw new Exception('Expected 200 response, received ' . $statusCode);
            }

            Log::info($statusCode.': Disbursement Response: ' . $body);

            return [
                'status' => $statusCode,
                'message' => self::STATUS_PENDING, // We must check for transaction status
                'reference' => $result['transactionid'],
                'payment_reference' => $result['transactionid'],
                'payment_message' => null,
                'status_code' => $statusCode,
                'service_provider' => $this->getServiceProviderName(),
                'data' => $result,
            ];
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            if ($e->hasResponse()) {
                Log::error('Disbursement Error: ' . $e->getResponse()->getBody()->getContents());
            }

            return [
                'status' => 'TF',
                'message' => self::STATUS_FAILED, // We must check for transaction status
                'reference' => null,
                'payment_reference' => null,
                'payment_message' => null,
                'status_code' => 500,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [],
            ];
        } catch (\Exception $e) {
            Log::error('Disbursement failed: ' . $e->getMessage());

            return [
                'status' => 'TF',
                'message' => self::STATUS_FAILED, // We must check for transaction status
                'reference' => null,
                'payment_reference' => null,
                'payment_message' => null,
                'status_code' => 500,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [],
            ];
        } catch (GuzzleException $e) {
            Log::error('Disbursement failed: ' . $e->getMessage());
            return [
                'status' => 'TF',
                'message' => self::STATUS_FAILED, // We must check for transaction status
                'reference' => null,
                'payment_reference' => null,
                'payment_message' => null,
                'status_code' => 500,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [],
            ];
        }
    }

    /**
     * @throws GuzzleException
     */
    public function disbursementStatus($txn_id): array
    {
        return $this->getTransactionStatus($txn_id);
    }

    /**
     * Debit money from an agent's wallet
     *
     * @param string $phone_number Agent's phone number (format: 256XXXXXXXXX)
     * @param float $amount Amount to debit
     * @param string $txn_reference External transaction reference
     * @param string $reason Reason for the debit
     * @return array Response from the API
     * @throws MtnApiException|GuzzleException
     */
    public function collect(
        $phone_number,
        $amount,
        $txn_reference,
        $reason = "Repayment"
    ): array {
        $this->validatePhoneNumber($phone_number);
        $this->validateAmount($amount);

        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns2:bankdebitrequest xmlns:ns2="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
            '    <fromfri>FRI:' . $phone_number . '/MSISDN</fromfri>' . PHP_EOL .
            '    <tofri>FRI:WKD-PERSONAL/USER</tofri>'. PHP_EOL .
            '    <amount>'. PHP_EOL .
            '        <amount>'. $amount . '</amount>'. PHP_EOL .
            '        <currency>UGX</currency>'. PHP_EOL .
            '    </amount>'. PHP_EOL .
            '    <externaltransactionid>' . $txn_reference . '</externaltransactionid>'. PHP_EOL .
            '    <frommessage>Loan Repayment.</frommessage>'. PHP_EOL .
            '    <referenceid>' . $txn_reference . '</referenceid>'. PHP_EOL .
            '    <simplesweep>' . false . '</simplesweep>'. PHP_EOL .
            '</ns2:bankdebitrequest>';

        Log::info('MTN Payload - Collection: ' . $payload);

        try {
            $response = $this->getClient()->post('/sp/bankdebit', [
                'body' => $payload
            ]);

            // Process successful response
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            $result = $this->xmlToArray($body);

            Log::info($statusCode.': Collect Response: ' . $body);
            $message = self::STATUS_PENDING;

            if (Arr::get($result, 'status') === 'SUCCESSFUL') {
                $message = self::STATUS_SUCCESS;
            } else if ($result['status'] === 'FAILED') {
                $message = self::STATUS_FAILED;
            }

            return [
                'status' => $statusCode,
                'message' => $message,
                'reference' => data_get($result, 'transactionid'),
                'payment_reference' => data_get($result, 'transactionid'),
                'payment_message' => null,
                'status_code' => $statusCode,
                'service_provider' => $this->getServiceProviderName(),
                'data' => $result,
            ];
        } catch (\Exception $e) {
            Log::error('Collection failed: ' . $e->getMessage());

            return [
                'status' => 500,
                'message' => self::STATUS_FAILED, // We must check for transaction status
                'reference' => null,
                'payment_reference' => null,
                'payment_message' => 'Collection failed: ' . str($e->getMessage())->limit(50),
                'status_code' => 500,
                'service_provider' => $this->getServiceProviderName(),
                'data' => [],
            ];
        }
    }

    /**
     * @throws GuzzleException
     */
    public function collectionStatus($collection_txn_id): array
    {
        return $this->getTransactionStatus($collection_txn_id);
    }

    /**
     * @throws GuzzleException
     */
    public function getTransactionStatus(string $referenceId): array
    {
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns2:gettransactionstatusrequest xmlns:ns2="http://www.ericsson.com/em/emm/financial/v1_1"' . PHP_EOL .
            '    <referenceid>' . $referenceId . '</referenceid>' . PHP_EOL .
            '    <identity>ID:EQI-cash.sp1/USER</identity>' . PHP_EOL .
            '</ns2:gettransactionstatusrequest>';

        Log::info('MTN Payload - Transaction Status:' . $payload);

        try {
            $response = $this->getClient()->post('/sp/gettransactionstatus', [
                'body' => $payload
            ]);

            // Process successful response
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();
            $result = $this->xmlToArray($body);

            Log::info($statusCode.': Transaction Status Response: ' . $body);
            $message = self::STATUS_PENDING;

            if (Arr::get($result, 'status') === 'SUCCESSFUL') {
                $message = self::STATUS_SUCCESS;
            } else if (Arr::get($result, 'status') === 'FAILED') {
                $message = self::STATUS_FAILED;
            }

            return [
                'status' => $statusCode,
                'message' => $message,
                'reference' => $referenceId,
                'payment_reference' => data_get($result, 'financialtransactionid'),
                'payment_message' => null,
                'status_code' => $statusCode,
                'data' => $result,
            ];
        } catch (\Exception $e) {
            Log::error('Transaction failed: ' . $e->getMessage());

            return [
                'status' => 500,
                'message' => self::STATUS_PENDING, // Network or internal error, we shall check again.
                'reference' => $referenceId,
                'payment_reference' => null,
                'payment_message' => 'Transaction failed: ' . $e->getMessage(),
                'status_code' => 500,
                'data' => [],
            ];
        }
    }

    /**
     * Register a new customer
     *
     * @param array $data Customer registration data with the following keys:
     *                    - msisdn (string): Customer phone number (format: 256XXXXXXXXX)
     *                    - firstName (string): Customer's first name
     *                    - lastName (string): Customer's last name
     *                    - dateOfBirth (string): Customer's date of birth (format: YYYY-MM-DD)
     *                    - idType (string): ID type (e.g., 'NRIN')
     *                    - idNumber (string): ID number
     *                    - idExpiry (string): ID expiry date (format: YYYY-MM-DD+HH:mm)
     * @return array Response from the API
     * @throws MtnApiException
     */
    public function registerCustomer(array $data): array
    {
        $this->validateCustomerRegistrationDetails($data);

        $payload = [
            'resource' => "FRI:{$data['msisdn']}/MSISDN",
            'name' => [
                'firstname' => $data['firstName'],
                'lastname' => $data['lastName']
            ],
            'dob' => $data['dateOfBirth'],
            'idtype' => $data['idType'],
            'idnumber' => $data['idNumber'],
            'idexpiry' => $data['idExpiry'],
            'addresses' => []
        ];

        return $this->makeXmlRequest('customerregistration', $payload);
    }

    /**
     */
    public function getCustomerDetails(string $msisdn): array
    {
        $customer = Customer::query()->where('Telephone_Number', $msisdn)->first();

        if (empty($customer)) {
            return [
                'status' => 'UNREGISTERED',
                'savingsaccounts' => [],
                'loanaccounts' => []
            ];
        }


        $details = [
                'status' => 'REGISTERED',
                'customerid' => $customer->Telephone_Number,
                'customerName' => $customer->name,
                'savingsaccounts' => [
                    'savingsaccount' => [
                        'accountnumber' => data_get($customer->options, 'savingsaccount.accountnumber'),
                        'status' => 'ACTIVE',
                        'balance' => [
                            'amount' => data_get($customer->options, 'savingsaccount.balance.amount'),
                            'currency' => 'UGX'
                        ],
                        'savingsaccounttype' => 'SAVINGS'
                    ]
                ],
                'loanaccounts' => []
            ];

        // Todo: Add loan accounts

        return $details;
    }

    /**
     * @throws ConnectionException
     */
    public function optOut(string $phoneNumber): true
    {
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:unlinkfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend">' . PHP_EOL .
            '    <fri>FRI:'. $phoneNumber . '@WKD-SAVINGS/SP</fri>' . PHP_EOL .
            '    <customerid>' . $phoneNumber . '</customerid>'. PHP_EOL .
            '</ns0:unlinkfinancialresourceinformationrequest>';

        $response = Http::baseUrl($this->baseUrl)
            ->withHeaders([
                'Content-Type' => 'application/xml',
                'Accept' => 'application/xml',
                'Authorization' => 'Basic ' . base64_encode("{$this->savingsUsername}:{$this->savingsPassword}")
            ])
            ->withOptions([
                'verify' => false,
                'ssl_key' => storage_path($this->lmsPrivateKey),
                'ssl_cert' => storage_path($this->dfcuCertificate),
                'ssl_cafile' => storage_path($this->dfcuCaCertificate)
            ])
            ->post("sp/unlinkfinancialresourceinformation", $payload);

        if ($response->failed()) {
            $this->logResponse('unlinkfinancialresourceinformationrequest', $response->body());

            throw new Exception($response->body());
        }

        Log::info('Opt out completed ' . $phoneNumber);

        return true;
    }

    /**
     * Complete customer registration process
     *
     * @param Customer $customer
     * @return bool Response from the API
     * @throws ConnectionException|\GuzzleHttp\Exception\GuzzleException
     */
    public function customerRegistrationCompleted(Customer $customer): bool
    {
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:customerregistrationcompletedrequest xmlns:p="http://www.ericsson.com/em/emm/sp/frontend">' . PHP_EOL .
            '    <resource>FRI:' . $customer->Telephone_Number . '@WKD-SAVINGS/SP</resource>' . PHP_EOL .
            '    <customerid>' . $customer->Telephone_Number . '</customerid>' . PHP_EOL .
            '    <status>REGISTERED</status>' . PHP_EOL .
            '    <savingsaccount>' . PHP_EOL .
            '        <accountnumber>' . data_get($customer->options, 'savingsaccount.accountnumber') . '</accountnumber>' . PHP_EOL .
            '        <status>ACTIVE</status>' . PHP_EOL .
            '        <balance>' . PHP_EOL .
            '            <amount>0</amount>' . PHP_EOL .
            '            <currency>UGX</currency>' . PHP_EOL .
            '        </balance>' . PHP_EOL .
            '        <savingsaccounttype>SAVINGS</savingsaccounttype>' . PHP_EOL .
            '    </savingsaccount>' . PHP_EOL .
            '    <message>Successfully registered with MoMoney.</message>' . PHP_EOL .
            '</p:customerregistrationcompletedrequest>';

        try {
            $response = $this->getClient('savings')->post('/sp/customerregistrationcompleted', [
                'body' => $payload
            ]);

            // Process successful response
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            $responseDetails = $this->xmlToArray($body);
            Log::info('MTN CR Completed: ' . json_encode($responseDetails));
            Log::info($statusCode.': MTN CR Completed: ' . $body);

            return true;
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            Log::error('Customer Registration Error: ' . $e->getMessage());

            if ($e->hasResponse()) {
                Log::error('Customer Registration Error: ' . $e->getResponse()->getBody());
            }

            return false;
        }
    }

    /**
     * Complete loan application process
     *
     * @param Transaction $transaction
     * @return bool
     * @throws \Exception
     */
    public function loanApplicationCompleted(Transaction $transaction): bool
    {
        $loanApplicationSession = $transaction->loanApplication->loan_session;
        $loanApplicationSession->loadMissing('loanProductTerm');
        $loanProductTerm = $loanApplicationSession->loanProductTerm;
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:loanapplicationcompletedrequest xmlns:p="http://www.ericsson.com/em/emm/sp/frontend" xmlns:op="http://www.ericsson.com/em/emm/common" xmlns:java="http://www.oracle.com/XSL/Transform/java" xmlns:tns="http://www.temenos.com/T24/event/RWFInbound/CusAsyncResp">' . PHP_EOL .
            '    <resource>FRI:' . $transaction->Telephone_Number . '@WKD-PERSONAL/SP</resource>' . PHP_EOL .
            '    <customerid>' . $transaction->Telephone_Number . '</customerid>' . PHP_EOL .
            '    <loanaccount>' . PHP_EOL .
            '        <accountnumber>' . $transaction->Loan_Application_ID . '</accountnumber>' . PHP_EOL .
            '        <status>APPROVED</status>' . PHP_EOL .
            '        <due>' . PHP_EOL .
            '            <amount>' . (int) $transaction->Amount . '</amount>' . PHP_EOL .
            '            <currency>UGX</currency>' . PHP_EOL .
            '        </due>' . PHP_EOL .
            '        <duedate>' . $loanApplicationSession->Maturity_Date->toDateString() . '</duedate>' . PHP_EOL .
            '        <tenor>' . $loanProductTerm->Value . '</tenor>' . PHP_EOL .
            '        <loantype>PERSONAL</loantype>' . PHP_EOL .
            '        <interest>0</interest>' . PHP_EOL .
            '    </loanaccount>' . PHP_EOL .
            '    <message>Hello, your loan request with Weekend Loan has been accepted. Please wait for the funds to be deposited to your MM account.</message>' . PHP_EOL .
            '</p:loanapplicationcompletedrequest>';

        Log::info('MTN LA Payload: ' . $payload);

        try {
            $response = $this->getClient()
                ->request('POST', '/sp/loanapplicationcompleted', [
                    'body' => $payload
                ]);

            $body = $response->getBody()->getContents();
            // Process successful response
            $details = $this->xmlToArray($body);

            if (Arr::has($details, 'errorcode')) {
                throw new \Exception('MTN LA Error: ' . json_encode($details));
            }

            Log::info('MTN LA Completed: ' . json_encode($details));

            return true;
        } catch (\Exception $e) {
            Log::error('MTN LA Error: ' . $e->getMessage(), $e->getTrace());

            return false;
        } catch (GuzzleException $e) {
            Log::error('MTN LA Error: ' . $e->getMessage(), $e->getTrace());
            return false;
        }
    }

    /**
     * Complete bank debit process
     *
     * @param Transaction $transaction
     * @return array Response from the API
     * @throws ConnectionException|GuzzleException
     */
    public function bankDebitCompleted(Transaction $transaction): array {
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:bankdebitcompletedrequest xmlns:ns0="http://www.ericsson.com/em/emm/sp/backend">' . PHP_EOL .
            '    <fromfri>FRI:' . $transaction->Telephone_Number . '/ID</fromfri>' . PHP_EOL .
            '    <tofri>FRI:WKD-PERSONAL/USER</tofri>' . PHP_EOL .
            '    <transactionid>' . $transaction->Provider_TXN_ID . '</transactionid>' . PHP_EOL .
            '    <amount>' . PHP_EOL .
            '        <amount>' . $transaction->Amount . '</amount>' . PHP_EOL .
            '        <currency>UGX</currency>' . PHP_EOL .
            '    </amount>' . PHP_EOL .
            '</ns0:bankdebitcompletedrequest>';

        try {
            $response = $this->getClient()->post('/sp/bankdebitcompleted', [
                'body' => $payload
            ]);

            // Process successful response
            $statusCode = $response->getStatusCode();
            $body = $response->getBody()->getContents();

            Log::info($statusCode.': MTN BD Completed: ' . $body);

            return $this->xmlToArray($body);
        } catch (\GuzzleHttp\Exception\RequestException $e) {
            Log::error('Customer Registration Error: ' . $e->getMessage());

            if ($e->hasResponse()) {
                Log::error('Customer Registration Error: ' . $e->getResponse()->getBody());

                return $this->xmlToArray($e->getResponse()->getBody());
            }

            return [];
        }
    }

    /**
     * Validate a customer
     *
     * @param string $externalRequestId External request ID
     * @param string $message Validation message
     * @param string $receiver Receiver ID
     * @param array $extension Additional extension data
     * @return array Response from the API
     * @throws MtnApiException
     */
    public function customerValidation(
        string $externalRequestId,
        string $message,
        string $receiver,
        array $extension = []
    ): array {
        $payload = [
            'externalrequestid' => $externalRequestId,
            'message' => $message,
            'receiver' => $receiver,
            'extension' => $extension
        ];

        return $this->makeXmlRequest('customervalidation', $payload);
    }

    /**
     * Initiate a loan application
     *
     * @param string $msisdn Customer's phone number
     * @param float $amount Loan amount
     * @return array Response from the API
     * @throws MtnApiException
     */
    public function createLoanApplication(
        string $msisdn,
        float $amount,
        int $tenor
    ): array {
        $this->validatePhoneNumber($msisdn);
        $this->validateAmount($amount);
        $this->validateTenor($tenor);

        $payload = [
            'resource' => "FRI:{$msisdn}/MSISDN",
            'amount' => [
                'amount' => $amount,
                'currency' => 'UGX'
            ],
            'tenor' => $tenor,
            'loantype' => 'PERSONAL'
        ];

        return $this->makeXmlRequest('initiateloanapplication', $payload);
    }

    /**
     * @param string $msisdn
     * @return array
     * @throws MtnApiException
     * @throws ConnectionException
     */
    public function getLoanLimit(string $msisdn): array
    {
        $this->validatePhoneNumber($msisdn);

        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<ns0:getfinancialresourceinformationrequest xmlns:ns0="http://www.ericsson.com/em/emm/serviceprovider/v1_0/backend/client">' . PHP_EOL .
            '    <resource>FRI:'. $msisdn . '@WKD-PERSONAL/SP</resource>' . PHP_EOL .
            '    <accountholderid>ID:' . $msisdn . '/MSISDN</accountholderid>'. PHP_EOL .
            '    <extension>'. PHP_EOL .
            '        <RequestType>CheckLoanLimit</RequestType>'. PHP_EOL .
            '    </extension>'. PHP_EOL .
            '</ns0:getfinancialresourceinformationrequest>';

        $response = Http::baseUrl($this->baseUrl)
            ->withHeaders([
                'Content-Type' => 'application/xml',
                'Accept' => 'application/xml',
                'Authorization' => 'Basic ' . base64_encode("{$this->loansUsername}:{$this->loansPassword}")
            ])
            ->withOptions([
                'verify' => false,
                'ssl_key' => storage_path($this->lmsPrivateKey),
                'ssl_cert' => storage_path($this->dfcuCertificate),
                'ssl_cafile' => storage_path($this->dfcuCaCertificate)
            ])
            ->get("sp/getfinancialresourceinformation", $payload);

        if ($response->failed()) {
            $this->logResponse('customerregistrationcompletedrequest', $response->body());

            return [];
        }

        Log::info('Loan Limit XML:' . $response->body());
        $result = $this->xmlToArray($response->body());
        Log::info('Loan Limit Response' . json_encode($result));

        return [
            'message' => data_get($result, 'message'),
            'amount' => data_get($result, 'extension.amount'),
        ];
    }

    /**
     * @throws MtnApiException
     */
    public function getMiniStatement(string $msisdn): array
    {
        $this->validatePhoneNumber($msisdn);

        $payload = [
            'resource' => "FRI:{$msisdn}@MOM-SAVINGS/SP",
            'accountholderid' => "ID:{$msisdn}/MSISDN",
            'extension' => [
                'RequestType' => 'ViewLast5Transactions'
            ]
        ];

        return $this->makeXmlRequest('getfinancialresourceinformation', $payload);
    }

    /**
     * @throws MtnApiException
     */
    public function refund(string $transactionId, float $amount): array
    {
        $this->validateAmount($amount);

        $payload = [
            'financialtransactionid' => $transactionId,
            'receivermessage' => 'Transaction Failed',
            'amount' => [
                'amount' => $amount,
                'currency' => 'UGX'
            ]
        ];

        return $this->makeXmlRequest('refund', $payload);
    }

    /**
     * Complete customer validation process
     *
     * @param string $externalRequestId External request ID
     * @param string $receiver Receiver ID
     * @param string $validationResult Validation result
     * @param string $approvalId Approval ID
     * @param array $extension Additional extension data
     * @return array Response from the API
     * @throws MtnApiException
     */
    public function customerValidationCompleted(
        string $externalRequestId,
        string $receiver,
        string $validationResult,
        string $approvalId,
        array $extension = []
    ): array {
        $payload = [
            'externalrequestid' => $externalRequestId,
            'receiver' => $receiver,
            'validationresult' => $validationResult,
            'approvalid' => $approvalId,
            'extension' => $extension
        ];

        return $this->makeXmlRequest('customervalidationcompleted', $payload);
    }

    private function extractErrorCode(string $xml): ?string
    {
        try {
            $xml = simplexml_load_string($xml);
            return (string) $xml->errorcode ?? null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public function arrayToXml(array $data, string $endpoint): string
    {
        $xml = new \SimpleXMLElement('<?xml version="1.0" encoding="UTF-8"?><request/>');

        // Add namespace based on endpoint
        $namespace = $this->getNamespaceForEndpoint($endpoint);
        if ($namespace) {
            $xml->addAttribute('xmlns', $namespace);
        }

        $this->arrayToXmlRecursive($data, $xml);

        return $xml->asXML();
    }

    private function getNamespaceForEndpoint(string $endpoint): ?string
    {
        $namespaces = [
            'disbursement' => 'http://www.ericsson.com/em/emm/sp/frontend',
            'unlinkfinancialresourceinformation' => 'http://www.ericsson.com/em/emm/serviceprovider/v1_0/frontend',
            'gettransactionstatus' => 'http://www.ericsson.com/em/emm/financial/v1_1',
            'getcustomerdetails' => 'http://www.ericsson.com/em/emm/sp/backend',
            'customerregistration' => 'http://www.ericsson.com/em/emm/sp/backend',
            'customerregistrationcompleted' => 'http://www.ericsson.com/em/emm/sp/frontend',
            'loanapplicationcompleted' => 'http://www.ericsson.com/em/emm/sp/frontend',
            'bankdebitcompleted' => 'http://www.ericsson.com/em/emm/sp/backend',
            'bankdebit' => 'http://www.ericsson.com/em/emm/sp/frontend',
            'initiateloanapplication' => 'http://www.ericsson.com/em/emm/sp/backend',
            'getfinancialresourceinformation' => 'http://www.ericsson.com/em/emm/serviceprovider/v1_0/backend/client',
            'refund' => 'http://www.ericsson.com/em/emm/financial/v1_0',
            'customervalidation' => 'http://www.ericsson.com/em/emm/financial/v1_0',
            'customervalidationcompleted' => 'http://www.ericsson.com/em/emm/callback/v1_1'
        ];

        return $namespaces[$endpoint] ?? null;
    }

    private function arrayToXmlRecursive(array $data, \SimpleXMLElement &$xml): void
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $subNode = $xml->addChild($key);
                $this->arrayToXmlRecursive($value, $subNode);
            } else {
                $xml->addChild($key, htmlspecialchars($value));
            }
        }
    }

    private function xmlToArray(string $xml): array
    {
        if (empty($xml)) {
            return [];
        }

        // Implement XML to array conversion
        // This is a simplified example
        return json_decode(json_encode(simplexml_load_string($xml)), true);
    }

    /**
     * Validate a phone number format
     *
     * @param string $msisdn Phone number to validate
     * @throws MtnApiException
     */
    private function validatePhoneNumber(string $msisdn): void
    {
        if (!preg_match('/^256[0-9]{9}$/', $msisdn)) {
            throw new MtnApiException('Invalid phone number format. Must be in format: 256XXXXXXXXX');
        }
    }

    /**
     * Validate amount is positive
     *
     * @param float $amount Amount to validate
     * @throws MtnApiException
     */
    private function validateAmount(float $amount): void
    {
        if ($amount <= 0) {
            throw new MtnApiException('Amount must be greater than 0');
        }
    }

    /**
     * Validate tenor is within allowed range
     *
     * @param int $tenor Loan tenor in days
     * @throws MtnApiException
     */
    private function validateTenor(int $tenor): void
    {
        if ($tenor < 1 || $tenor > 3) {
            throw new MtnApiException('Tenor must be between 1 and 3 days');
        }
    }

    /**
     * Log API request for debugging
     *
     * @param string $endpoint API endpoint
     * @param array $data Request data
     * @param string $xml Request XML
     */
    private function logRequest(string $endpoint, array $data, string $xml): void
    {
        if ($this->debug) {
            logger()->debug('MTN API Request', [
                'endpoint' => $endpoint,
                'data' => $data,
                'xml' => $xml,
                'url' => "{$this->baseUrl}/{$this->baseContext}/{$endpoint}"
            ]);
        }
    }

    /**
     * Log API response for debugging
     *
     * @param string $endpoint API endpoint
     * @param string $response Response body
     */
    private function logResponse(string $endpoint, string $response): void
    {
        Log::error('MTN Response Error', [
            'endpoint' => $endpoint,
            'response' => $response
        ]);
    }

    private function makeRequest(): PendingRequest
    {
        return Http::baseUrl($this->baseUrl);
    }

    private function makeXmlRequest(string $endpoint, array $data): array
    {
        try {
            $xml = $this->arrayToXml($data, $endpoint);

            $this->logRequest($endpoint, $data, $xml);

            $response = $this->makeRequest()
                ->withHeaders([
                    'Content-Type' => 'application/xml',
                    'Accept' => 'application/xml'
                ])
                ->post("/$this->baseContext/$endpoint", $xml);

            $this->logResponse($endpoint, $response->body());

            if ($response->failed()) {
                $errorBody = $response->body();
                $errorCode = $this->extractErrorCode($errorBody);

                switch ($errorCode) {
                    case 'ACCOUNTHOLDER_NOT_FOUND':
                        throw new MtnApiException('Account holder not found in the system');
                    case 'RESOURCE_NOT_FOUND':
                        throw new MtnApiException('Requested financial resource does not exist');
                    case 'INVALID_AMOUNT':
                        throw new MtnApiException('Invalid transaction amount');
                    case 'INSUFFICIENT_FUNDS':
                        throw new MtnApiException('Insufficient funds for transaction');
                    case 'INVALID_MSISDN':
                        throw new MtnApiException('Invalid phone number format');
                    case 'SERVICE_UNAVAILABLE':
                        throw new MtnApiException('MTN service is currently unavailable');
                    default:
                        throw new MtnApiException("MTN API request failed: {$errorBody}");
                }
            }

            $result = $this->xmlToArray($response->body());
            $this->validateResponse($endpoint, $result);
            return $result;
        } catch (\Exception $e) {
            logger()->error('MTN API Error', [
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
                'data' => $data,
                'trace' => $e->getTraceAsString()
            ]);

            throw new MtnApiException("Failed to process MTN API request: {$e->getMessage()}");
        }
    }

    /**
     * Validate API response based on endpoint
     *
     * @param string $endpoint API endpoint
     * @param array $response Response data
     * @throws MtnApiException
     */
    private function validateResponse(string $endpoint, array $response): void
    {
        switch ($endpoint) {
            case 'disbursement':
                if (!isset($response['transactionid'])) {
                    throw new MtnApiException('Invalid disbursement response: missing transaction ID');
                }
                break;
            case 'getcustomerdetails':
                if (!isset($response['status'])) {
                    throw new MtnApiException('Invalid customer details response: missing status');
                }
                break;
            case 'initiateloanapplication':
                if (!isset($response['loanaccount'])) {
                    throw new MtnApiException('Invalid loan application response: missing loan account details');
                }
                break;
        }
    }

    public function processCallback(Transaction $transaction): bool
    {
        try {
            $txn_reference = $transaction->TXN_ID;

            if (!$transaction) {
                throw new Exception("Transaction with reference $txn_reference not found");
            }

            if (strtoupper($transaction->Status) == self::STATUS_SUCCESS) {
                throw new Exception("Transaction with reference $txn_reference already processed");
            }

            if ($transaction->Type == Transaction::DISBURSEMENT) {
                return $this->processLoanDisbursement($transaction);
            } else if ($transaction->Type == Transaction::DEPOSIT) {
                return $this->processSavingsDeposit($transaction);
            } else if ($transaction->Type == Transaction::WITHDRAW) {
                return $this->processSavingsWithdraw($transaction);
            } else if ($transaction->Type == Transaction::REPAYMENT) {
                return $this->processLoanRepayment($transaction);
            } else if ($transaction->Type == Transaction::RESTRUCTURE) {
                return $this->processLoanRestructure($transaction);
            } else if ($transaction->Type == Transaction::DOWNPAYMENT) {
                return $this->processAssetLoanDownpayment($transaction);
            } else {
                throw new Exception("Invalid transaction type");
            }
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return false;
        }
    }

    private function processLoanRepayment($transaction): bool
    {
        return app(ProcessLoanRepaymentAction::class)->execute($transaction);
    }

    private function processLoanDisbursement($transaction): bool
    {
        return app(CreateApprovedLoanAction::class)->execute($transaction);
    }

    private function processSavingsDeposit($transaction): bool
    {
        return app(ProcessSavingsDepositAction::class)->execute($transaction);
    }

    private function processSavingsWithdraw($transaction): bool
    {
        return app(ProcessSavingsWithdrawalAction::class)->execute($transaction);
    }

    private function processLoanRestructure($transaction): bool
    {
        return app(ProcessLoanRestructureAction::class)->execute($transaction);
    }

    private function processAssetLoanDownpayment($transaction): bool
    {
        return app(AffectAssetLoanDownPaymentAction::class)->execute($transaction);
    }

    /**
     * @param array $data
     * @return void
     * @throws MtnApiException
     */
    public function validateCustomerRegistrationDetails(array $data): void
    {
        $validator = Validator::make($data, [
            'msisdn' => ['required', 'string', 'regex:/^256[0-9]{9}$/'],
            'firstName' => ['required', 'string'],
            'lastName' => ['required', 'string'],
            'dateOfBirth' => ['required', 'date_format:Y-m-d'],
            'idType' => ['required', 'string'],
            'idNumber' => ['required', 'string'],
            'idExpiry' => ['required', 'string', 'regex:/^\d{4}-\d{2}-\d{2}\+\d{2}:\d{2}$/'],
        ]);

        if ($validator->fails()) {
            throw new MtnApiException('Invalid customer registration data: ' .
                $validator->errors()->first());
        }
    }

    /**
     * @return Client
     */
    public function getClient($type = 'loans'): Client
    {
        $auth = [$this->loansUsername, $this->loansPassword];

        if ($type == 'savings') {
            $auth = [$this->savingsUsername, $this->savingsPassword];
        }

        return new Client([
            'base_uri' => $this->baseUrl,
            'auth' => $auth,
            'headers' => [
                'Content-Type' => 'application/xml',
                'Accept' => 'application/xml',
            ],
            'verify' => false,
            'cert' => storage_path($this->dfcuCertificate),
            'ssl_key' => storage_path($this->lmsPrivateKey),
            'curl' => [
                CURLOPT_CAINFO => storage_path($this->dfcuCaCertificate),
            ],
        ]);
    }

    public function getBaseUrl()
    {
        return $this->baseUrl;
    }

    public function getLoansUsername()
    {
        return $this->loansUsername;
    }

    public function getLoansPassword()
    {
        return $this->loansPassword;
    }

    public function getDfcuCertificate()
    {
        return $this->dfcuCertificate;
    }

    public function getLmsPrivateKey()
    {
        return $this->lmsPrivateKey;
    }

    public function getDfcuCaCertificate()
    {
        return $this->dfcuCaCertificate;
    }

    public function getServiceProviderName(): string
    {
        return 'MTN';
    }
}
