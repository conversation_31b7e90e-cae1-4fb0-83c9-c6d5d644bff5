<?php

namespace App\Http\Controllers;

use App\Imports\CashSalesImport;
use App\Models\CashSale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class CashSaleController extends Controller
{
    public function index()
    {
        $sales = CashSale::latest()->paginate(10);
        return view('cash-sales.index', compact('sales'));
    }

    public function create()
    {
        return view('cash-sales.create');
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_location' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'receipt' => 'required|file|mimes:jpeg,png,pdf|max:2048',
        ]);

        // Generate receipt number
        $receiptNumber = 'CS-' . date('Ymd') . '-' . strtoupper(substr(uniqid(), -6));

        // Store receipt file
        $path = $request->file('receipt')->store('receipts', 'public');

        CashSale::create([
            'receipt_number' => $receiptNumber,
            'customer_name' => $validated['customer_name'],
            'customer_phone' => $validated['customer_phone'],
            'customer_location' => $validated['customer_location'],
            'amount' => $validated['amount'],
            'receipt_path' => $path,
            'partner_id' => Auth::user()->partner_id,
            'vin_no' => $request->vin_no,
            'reg_no' => $request->reg_no,
            'tin' => $request->tin,
            'usage' => $request->usage,
            'sales_executive' => $request->sales_executive,
            'lead_source' => $request->lead_source,
            'financier' => $request->financier,
        ]);

        return redirect()->route('cash-sales.index')->with('success', 'Cash sale recorded successfully!');
    }

    public function show(CashSale $cashSale)
    {
        return view('cash-sales.show', compact('cashSale'));
    }
    // app/Http/Controllers/CashSaleController.php

    public function edit(CashSale $cashSale)
    {
        return view('cash-sales.edit', compact('cashSale'));
    }

    public function update(Request $request, CashSale $cashSale)
    {
        $validated = $request->validate([
            'customer_name' => 'required|string|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_location' => 'required|string|max:255',
            'amount' => 'required|numeric|min:0',
            'receipt' => 'nullable|file|mimes:jpeg,png,pdf|max:2048',
        ]);

        // Update receipt file if new one is provided
        if ($request->hasFile('receipt')) {
            // Delete old receipt
            Storage::delete($cashSale->receipt_path);

            // Store new receipt
            $path = $request->file('receipt')->store('receipts', 'public');
            $validated['receipt_path'] = $path;
        }

        $cashSale->update([
            'customer_name' => $validated['customer_name'],
            'customer_phone' => $validated['customer_phone'],
            'customer_location' => $validated['customer_location'],
            'amount' => $validated['amount'],
            'receipt_path' => $validated['receipt_path'] ?? $cashSale->receipt_path,
            'vin_no' => $request->vin_no,
            'reg_no' => $request->reg_no,
            'tin' => $request->tin,
            'usage' => $request->usage,
            'sales_executive' => $request->sales_executive,
            'lead_source' => $request->lead_source,
            'financier' => $request->financier,
        ]);

        return redirect()->route('cash-sales.index')
            ->with('success', 'Cash sale updated successfully!');
    }

    public function bulkUploadForm()
    {
        return view('cash-sales.bulk-upload');
    }

    public function bulkUpload(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:2048'
        ]);

        try {
            $import = new CashSalesImport;
            Excel::import($import, $request->file('csv_file'));

            return redirect()->route('cash-sales.index')
                ->with('success', 'Successfully imported records');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error during import: ' . $e->getMessage())
                ->withInput();
        }
    }
}
