<?php

namespace App\Http\Controllers\DecisionEngine;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Partner;
use App\Models\DecisionEngine\IncomeMultiplier;
use App\Validators\UniquePartnerMinMax;
use App\Services\DecisionEngineService;
use Illuminate\Foundation\Validation\ValidatesRequests;

class IncomeMultiplierController extends Controller
{
    use ValidatesRequests;

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        return view("decision-engine.income-multipliers.index");
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        try {
            $partnerId = auth()->user()->partner_id;
            $partners = is_null($partnerId)
                ? Partner::orderBy("Institution_Name", "asc")
                    ->get()
                : Partner::whereId($partnerId)
                    ->get();

            return view("decision-engine.income-multipliers.create", compact("partners"));
        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Set default values before validation
        $request->merge([
            'minimum' => $request->input('minimum') ?? DecisionEngineService::MIN_LIMIT,
            'maximum' => $request->input('maximum') ?? DecisionEngineService::MAX_LIMIT,
        ]);

        $rules = [
            'partner_id' => 'nullable',
            'minimum' => 'nullable|required_without:maximum|numeric',
            'maximum' => [
                'nullable',
                'required_without:minimum',
                'numeric',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->input('minimum') !== null && $value < $request->input('minimum')) {
                        $fail('The maximum must be greater than or equal to the minimum.');
                    }
                },
            ],
            'multiplier' => 'required|numeric',
            new UniquePartnerMinMax(
                "income_multipliers",
                "partner_id",
                $request->partner_id,
                $request->minimum,
                $request->maximum
            ),
        ];

        $customMessages = [
            'partner_id.unique' => 'The partner ID must be unique.',
            'minimum.required_without' => 'The minimum value is required when maximum is not present.',
            'minimum.numeric' => 'The minimum value must be a number.',
            'maximum.required_without' => 'The maximum value is required when minimum is not present.',
            'maximum.numeric' => 'The maximum value must be a number.',
            'multiplier.required' => 'The multiplier is required.',
            'multiplier.numeric' => 'The multiplier must be a number.',
        ];

        $this->validate($request, $rules, $customMessages);

        try {
            $data = $request->all();

            IncomeMultiplier::create($data);

            return redirect()
                ->route("decision-engine.income-multipliers.index")
                ->withSuccess("Multiplier created successfully");
        } catch (\Throwable $r) {
            return redirect()->back()->withErrors($r->getMessage())->withInput();
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    { }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        try {
            $data = IncomeMultiplier::find($id);
            $partnerId = auth()->user()->partner_id;
            $partners = is_null($partnerId)
                ? Partner::orderBy("Institution_Name", "asc")
                    ->get()
                : Partner::whereId($partnerId)
                    ->get();

            return view("decision-engine.income-multipliers.edit", compact("partners", "data"));
        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Set default values before validation
        $request->merge([
            'minimum' => $request->input('minimum') ?? DecisionEngineService::MIN_LIMIT,
            'maximum' => $request->input('maximum') ?? DecisionEngineService::MAX_LIMIT,
        ]);

        $this->validate($request, [
            'partner_id' => 'nullable|exists:partners,id', // Ensure partner_id exists in the partners table if provided
            'minimum' => 'nullable|required_without:maximum|numeric',
            'maximum' => [
                'nullable',
                'required_without:minimum',
                'numeric',
                function ($attribute, $value, $fail) use ($request) {
                    if ($request->input('minimum') !== null && $value < $request->input('minimum')) {
                        $fail('The maximum must be greater than or equal to the minimum.');
                    }
                },
            ],
            'multiplier' => 'required|numeric|min:0',
            new UniquePartnerMinMax(
                "income_multipliers",
                "partner_id",
                $request->partner_id,
                $request->minimum,
                $request->maximum,
                $id
            ),
        ], [
            // Custom validation messages
            'partner_id.exists' => 'The selected partner does not exist.',
            'minimum.required_without' => 'The minimum value is required when maximum is not present.',
            'minimum.numeric' => 'The minimum value must be a valid number.',
            'maximum.required_without' => 'The maximum value is required when minimum is not present.',
            'maximum.numeric' => 'The maximum value must be a valid number.',
            'multiplier.required' => 'The multiplier is required.',
            'multiplier.numeric' => 'The multiplier must be a valid number.',
            'multiplier.min' => 'The multiplier must be at least 0.',
        ]);

        try {
            // Update the record
            $data = $request->only(['partner_id', 'minimum', 'maximum', 'multiplier']);
            IncomeMultiplier::where('id', $id)->update($data);

            return redirect()
                ->route("decision-engine.income-multipliers.index")
                ->withSuccess("Multiplier updated successfully.");
        } catch (\Throwable $e) {
            // Handle the exception and provide a more detailed error message
            return redirect()->back()->withErrors("An error occurred: " . $e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $data = IncomeMultiplier::findOrFail($id);
            $data->delete();
            return redirect()
                ->back()
                ->withSuccess("Multiplier deleted successfully");
        } catch (\Throwable $r) {
            return redirect()
                ->back()
                ->withErrors($r->getMessage());
        }
    }
}
