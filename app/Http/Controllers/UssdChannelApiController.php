<?php

namespace App\Http\Controllers;

use App\Actions\Loans\OptOutCustomerAction;
use App\Models\CreditScore;
use Exception;
use Carbon\Carbon;
use App\Models\Loan;
use App\Models\Partner;
use App\Models\Customer;
use App\Models\LoanProduct;
use App\Models\Transaction;
use Illuminate\Support\Str;
use App\Models\LoanSchedule;
use Illuminate\Http\Request;
use App\Models\AssetLocation;
use App\Services\LoanService;
use App\Models\SavingsProduct;
use App\Models\LoanApplication;
use App\Models\LoanDownpayment;
use App\Models\LoanProductTerm;
use App\Models\LoanProductType;
use App\Models\SavingsProvider;
use App\Services\MtnApiService;
use App\Models\Accounts\Account;
use App\Models\SavingsProductFee;
use Illuminate\Support\Facades\DB;
use App\Models\SavingProductPeriod;
use App\Services\MockMtnApiService;
use Illuminate\Support\Facades\Log;
use App\Models\CustomerSavingPeriod;
use Illuminate\Support\Facades\Http;
use App\Exceptions\ExpectedException;
use App\Models\LmsUssdSessionTracking;
use Illuminate\Support\Facades\Validator;
use App\Models\CustomerSavingsPreferences;
use App\Services\Account\AccountSeederService;
use App\Http\Resources\CustomerLoansApiResource;
use PhpOffice\PhpSpreadsheet\Calculation\DateTimeExcel\Days;

class UssdChannelApiController extends Controller
{
    public function getLastTransaction(Request $request)
    {
        try {
            $request->validate([
                "phoneNumber" => "required",
            ]);
            $phoneNumber = $request->phoneNumber;
            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }

            $transaction = Transaction::where('Telephone_Number', $phoneNumber)->where('Status', 'Completed')->first();
            if (!$transaction) {
                throw new Exception('Transaction not found.', 400);
            }
            return response()->json([
                "returnCode" => 0,
                "returnData" => [
                    'transaction' => $transaction
                ]
            ]);
        } catch (Exception $e) {
            return response()->json([
                "returnCode" => 21,
                "returnMessage" => "An unexpected error occurred",
                "error" => $e->getMessage()
            ], 200);
        }
    }

    public function getCustomerDetails(Request $request, MtnApiService $mtnApiService)
    {

        // Todo: Add loan accounts


        try {
            $request->validate([
                "phoneNumber" => "required",
            ]);

            $customer = Customer::query()->where('Telephone_Number', $request->phoneNumber)->first();
            $details = [
                'status' => 'UNREGISTERED',
                'savingsaccounts' => [],
                'loanaccounts' => []
            ];

            if (empty($customer)) {
                return response()->json([
                    "returnCode" => 0,
                    "returnData" => [
                        'customer' => $details,
                        'loanlimit' => [
                            'amount' => 1000000
                        ]
                    ]
                ]);
            }

            // todo: Add integration for check loan limit
            //$loanLimitDetails = $mtnApiService->getLoanLimit($request->phoneNumber);

            $details = [
                'status' => 'REGISTERED',
                'customerid' => $customer->Telephone_Number,
                'customerName' => $customer->name,
                'savingsaccounts' => [
                    'savingsaccount' => [
                        'accountnumber' => data_get($customer->options, 'savingsaccount.accountnumber'),
                        'status' => 'ACTIVE',
                        'balance' => [
                            'amount' => data_get($customer->options, 'savingsaccount.balance.amount'),
                            'currency' => 'UGX'
                        ],
                        'savingsaccounttype' => 'SAVINGS'
                    ]
                ],
                'loanaccounts' => [],
            ];

            // todo: Add loan accounts;

            return response()->json([
                "returnCode" => 0,
                "returnData" => [
                    'customer' => $details,
                    'loanlimit' => [
                        'amount' => 1000000
                    ]
                ]
            ]);
        } catch (Exception $e) {
            Log::error($e->getTraceAsString());
            return response()->json([
                "returnCode" => 21,
                "returnMessage" => "An unexpected error occurred",
                "error" => $e->getMessage()
            ], 200);
        }
    }
    public function postCreditScore(Request $request)
    {
        try {
            $request->validate([
                "requestId" => "required",
                "phoneNumber" => "required",
                "creditLimit" => "required",
                "overrideLimit" => "required|boolean",
            ]);

            $phoneNumber = $request->phoneNumber;
            $requestId = $request->requestId;
            $finalCreditLimit = $request->creditLimit;


            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            if (!$customer) {
                $data = $request->validate([
                    "firstName" => "required|string|max:100",
                    "lastName" => "required|string|max:100",
                    "gender" => "required|string|max:100|in:Male,Female,Other",
                    "maritalStatus" => "sometimes|string|max:100|in:Single (never married),Married,Divorced,Widowed,Separated,Annulled,Cohabitating,Other",
                    "dateOfBirth" => "required|date",
                    "idType" => "required|string",
                    "idNumber" => "required|string|max:100",
                    "classification" => "required|string|max:100|in:Individual,Non-Indvidual",
                    "phoneNumber" => "required|string|max:15",
                    'emailAddress' => 'nullable|email|max:100|unique:customers,Email_Address'
                ]);
                $customer = new Customer();
                $customer->First_Name = $data['firstName'];
                $customer->Last_Name = $data['lastName'];
                $customer->Telephone_Number = $data['phoneNumber'];
                $customer->Email_Address = $data['emailAddress'];
                $customer->ID_Type = $data['idType'];
                $customer->ID_Number = $data['idNumber'];
                $customer->Gender = $data['gender'];
                $customer->Marital_Status = $data['maritalStatus'];
                $customer->Date_of_Birth = $data['dateOfBirth'];
                $customer->Classification = $data['classification'];
                $customer->save();
            }
            $creditData = $request->all();
            $creditData['customerId'] = $customer->id;
            if (isset($creditData['mnoScore'])) {
                $this->storeCreditScore($creditData);
            }

            if ($request->overrideLimit) {
                // todo: Pending integration with MADAPI
                //$service = new MtnApiService();
                //$loanLimit = $service->getLoanLimit($phoneNumber);

                $finalCreditLimit = (int) 1000000;
            }

            // call crb api

            return response()->json([
                "returnCode" => 0,
                "returnData" => [
                    "phone" => $phoneNumber,
                    "requestId" => $requestId,
                    "finalCreditLimit" => $finalCreditLimit,
                    "scores" => [
                        "MNO" => [
                            "score" => '857',
                            "band" => 'AAA',
                            "rating" => 'Excellent',
                        ],
                        "CRB" => [
                            "score" => '857',
                            "band" => 'AAA',
                            "rating" => 'Excellent',
                        ],
                    ],
                ],
                "returnMessage" => "Successfully posted credit score"
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            Log::error($validationException->validator->errors());
            return response()->json([
                "returnCode" => 1,
                "returnMessage" => "Validation error",
                "errors" => $validationException->validator->errors()
            ], 200);
        } catch (\Throwable $throwable) {
            Log::error($throwable->getMessage());
            return response()->json([
                "returnCode" => 13,
                "returnMessage" => "An unexpected error occurred",
                "error" => $throwable->getMessage()
            ], 200);
        }
    }

    public function postLoanApplication(Request $request)
    {
        DB::beginTransaction();
        try {
            $request->validate([
                "requestId" => "required",
                "phoneNumber" => "required|string|max:13",
                "loanAmount" => "required",
                "loanProductCode" => "required|exists:loan_products,Code",
                "loanProductTermCode" => "required|exists:loan_product_terms,Code",
                "loanPurpose" => "required",
                "frequencyOfInstallmentRepayment" => "required",
                "loanTermInDays" => "required|numeric|min:1",
            ]);

            $requestId = $request->requestId;

            $phoneNumber = $request->phoneNumber;
            $requestId = $request->requestId;
            $loanAmount = $request->loanAmount;
            $loanTermInDays = $request->loanTermInDays;
            $frequencyOfInstallmentRepayment = $request->frequencyOfInstallmentRepayment;

            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();
            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }

            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $loan = $customer->loans()
                ->whereNot('Credit_Account_Status', 4) // Fully Paid
                ->whereNot('Credit_Account_Status', 3) // Written-off
                ->latest()
                ->first();

            if ($loan) {
                throw new Exception('Customer already has an active loan with ' . $loan->partner->Institution_Name . ' of UGX ' . number_format($loan->totalOutstandingBalance()) . ', Loan ID: ' . $loan->id, 400);
            }

            $loanProductCode = $request->loanProductCode;
            $loanProductTermCode = $request->loanProductTermCode;

            $loanProduct = LoanProduct::where('Code', $loanProductCode)->first();
            if (!$loanProduct) {
                throw new Exception('Loan product not found. You provided an invalid loan product code in the body.', 400);
            }

            if ($loanProduct->Partner_ID != $partner->id) {
                throw new Exception('Loan product provided does not belong to partner provided', 400);
            }
            $loanProductTerm = LoanProductTerm::where('Code', $loanProductTermCode)->first();
            if (!$loanProductTerm) {
                throw new Exception('Loan product term not found. You provided an invalid loan product term code in the body.', 400);
            }
            if ($loanProductTerm->Loan_Product_ID != $loanProduct->id) {
                throw new Exception('Loan product term provided does not belong to the loan product provided, Request: ' . json_encode($request->all()), 400);
            }
            if ($loanTermInDays > $loanProductTerm->Value) {
                throw new Exception('Loan term is too long.', 400);
            }
            if (!in_array($frequencyOfInstallmentRepayment, json_decode($loanProductTerm->Repayment_Cycles))) {
                throw new Exception('Unsupported frequency of installment repayment.', 400);
            }

            $isAssetLoan = $loanProduct->isAssetLoan();
            $total_Downpayment = 0;
            $totalRepaymentAmount = 0;
            $dailyRepaymentAmount = 0;
            $totalAddonsAmount = 0;
            $settlementFee = 0;
            if ($isAssetLoan) {
                // From the loan terms
                if ($loanProductTerm->Has_Advance_Payment) {
                    if ($loanProductTerm->Advance_Calculation_Method == "Percentage") {
                        $loanDownpayment = $loanAmount * ($loanProductTerm->Advance_Value / 100);
                    } else {
                        $loanDownpayment = $loanProductTerm->Advance_Value;
                    }
                    $loanAmount -= $loanDownpayment;
                    $total_Downpayment += $loanDownpayment;
                    $downPaymentSummary[] = ['name' => $loanProduct->Name, 'percentage' => $loanProductTerm->Advance_Value . ($loanProductTerm->Advance_Calculation_Method == "Percentage" ? '%' : ''), 'amount' => 'UGX ' . number_format($loanDownpayment)];
                }
            }
            $numberOfPayments = Loan::determineNumberOfPayments($loanTermInDays ?? $loanProductTerm->Value, $frequencyOfInstallmentRepayment);
            if ($loanProductTerm->Interest_Calculation_Method == "Flat") {
                $totalScheduleAmounts = LoanSchedule::calculateFlatTotalRepaymentAndInterest(
                    $loanAmount,
                    $loanProductTerm->Interest_Rate,
                    $loanTermInDays ?? $loanProductTerm->Value,
                    $frequencyOfInstallmentRepayment,
                    $loanProductTerm->Interest_Cycle,
                );
                $totalRepaymentAmount = $totalScheduleAmounts['total_repayment'];
                $dailyRepaymentAmount = $totalScheduleAmounts['daily_repayment'];
            } else if ($loanProductTerm->Interest_Calculation_Method == "Declining Balance - Discounted") {
                $totalScheduleAmounts = LoanSchedule::calculateDecliningBalanceTotalRepaymentAndInterest(
                    $loanAmount,
                    $loanProductTerm->Interest_Rate,
                    $numberOfPayments,
                    $frequencyOfInstallmentRepayment,
                    $loanProductTerm->Interest_Cycle,
                );
                $totalRepaymentAmount = $totalScheduleAmounts['total_repayment'];
                $dailyRepaymentAmount = $totalScheduleAmounts['daily_repayment'];
            } else if ($loanProductTerm->Interest_Calculation_Method == "Amortization") {
                $totalScheduleAmounts = LoanSchedule::calculateAmortizedTotalRepaymentAndInterest(
                    $loanAmount,
                    $loanProductTerm->Interest_Rate,
                    $numberOfPayments,
                    $frequencyOfInstallmentRepayment,
                    $loanProductTerm->Interest_Cycle,
                );
                $totalRepaymentAmount = $totalScheduleAmounts['total_repayment'];
                $dailyRepaymentAmount = $totalScheduleAmounts['daily_repayment'];
            } else if ($loanProductTerm->Interest_Calculation_Method == "Flat on Loan Amount") {
                $totalScheduleAmounts = LoanSchedule::calculateFlatAmountTotalRepaymentAndInterest(
                    $loanAmount,
                    $loanProductTerm->Interest_Rate,
                    $loanTermInDays ?? $loanProductTerm->Value,
                    $frequencyOfInstallmentRepayment,
                );
                $totalRepaymentAmount = $totalScheduleAmounts['total_repayment'];
                $dailyRepaymentAmount = $totalScheduleAmounts['daily_repayment'];
            }
            $credit_account_type = $loanProduct->loan_product_type; // asset or mobile loan
            $loanRecordDetails = [
                'Request_ID' => $requestId,
                'Partner_ID' => $partner->id,
                'Customer_ID' => $customer->id,
                'Loan_Product_ID' => $loanProduct->id,
                'Loan_Purpose' => $request->loanPurpose,
                'Applicant_Classification' => $customer->Classification,
                'Credit_Application_Date' => Carbon::now(),
                'Amount' => $loanAmount,
                'Credit_Application_Status' => 'Pending',
                'Credit_Account_or_Loan_Product_Type' => $credit_account_type->Code,
                'Credit_Application_Duration' => '0', // time between application and the time it is approved or rejected. This is auto so zero(0)
                'Client_Consent_flag' => 'Yes',
                'Country' => $customer->Country,
                'District' => $customer->District,
                'Subcounty' => $customer->Subcounty,
                'Parish' => $customer->Parish,
                'Village' => $customer->Village,
                'Last_Status_Change_Date' => Carbon::now()->toDateString(),
                'Credit_Amount_Approved' => $loanAmount,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];

            // Create a new loan application
            $loan_application = LoanApplication::updateOrCreate([
                'Request_ID' => $requestId,
                'Partner_ID' => $partner->id,
                'Customer_ID' => $customer->id,
                'Loan_Product_ID' => $loanProduct->id,
            ], $loanRecordDetails);

            $sessionData = [
                "requestId" => $requestId,
                "Customer_Phone_Number" => $phoneNumber,
                "Loan_Application_ID" => $loan_application->id,
                "Loan_Producd_Code" => $loanProductCode,
                "Loan_Producd_Term_Code" => $loanProductTermCode,
                "Credit_Payment_Frequency" => $frequencyOfInstallmentRepayment,
                "Number_of_Payments" => $numberOfPayments,
                'Date_of_First_Payment' => Loan::determineDateOfFirstPayment($frequencyOfInstallmentRepayment, $loanProductTerm->Value),
                'Maturity_Date' => Loan::determineMaturityDate($numberOfPayments ?? $loanProductTerm->Value, $frequencyOfInstallmentRepayment, $loanTermInDays),
                'hasPermit' => $request->hasPermit,
            ];

            $fees = $loanProduct->fees;

            $feeAmount = 0;
            $feesStructure = [];
            $facilitationFees = 0;
            $downPaymentSummary = [];
            $addons = [];
            // Store the loan application ID in the session
            $lmsUssdSessionTracking = LmsUssdSessionTracking::updateOrCreate([
                "requestId" => $requestId,
                "Customer_Phone_Number" => $phoneNumber,
                'Schedule_Loan_Amount' => $loanAmount,
            ], $sessionData);
            $data = [];
            if ($isAssetLoan) {

                // From the loan product add-ons
                if ($loanProduct->loan_product_addons()->count() > 0) {
                    $customerHasAPermit = $request->hasPermit;

                    foreach ($loanProduct->loan_product_addons as $addon) {
                        // Skip if the add-on is a permit and the customer already has one
                        if (Str::contains($addon->Name, 'Permit') && $customerHasAPermit) {
                            continue;
                        }

                        // Calculate downpayment and interest based on add-on type and method
                        [$addonAmount, $interestCalculated, $downPayment, $totalpayment] = Loan::calculateAddonDetails($addon, $loanAmount);
                        // Update total downpayment
                        $total_Downpayment += $downPayment;
                        $totalAddonsAmount += $totalpayment;
                        // $facilitationFees += $downPayment;

                        // Prepare add-ons summary
                        $addons[] = [
                            'name' => $addon->Name,
                            'amount' => 'UGX ' . number_format($addonAmount),
                            'payOff' => 'UGX ' . number_format($interestCalculated),
                            'tenure' => round($addon->Term / 30, 0) . ' Month(s)',
                            'interestRate' => $addon->Interest_Rate . '%'
                        ];

                        // Prepare downpayment summary
                        $downPaymentSummary[] = [
                            'name' => $addon->Name,
                            'percentage' => $addon->Downpayment_Percentage . '%',
                            'amount' => 'UGX ' . number_format($downPayment)
                        ];
                        $ninetyPercent = $addonAmount - $downPayment;

                        $loanAmount += $ninetyPercent;
                        if (Str::contains($addon->Name, 'Insurance')) {
                            $lmsUssdSessionTracking->Insurance_Amount = $addonAmount;
                            $lmsUssdSessionTracking->save();
                        }

                        if (Str::contains($addon->Name, 'Permit')) {
                            $lmsUssdSessionTracking->Permit_Amount = $addonAmount;
                            $lmsUssdSessionTracking->save();
                        }
                        $addonTotal = LoanSchedule::calculateFlatTotalRepaymentAndInterest(
                            $ninetyPercent,
                            $addon->Interest_Rate,
                            $addon->Term,
                            $addon->Repayment_Cycle,
                            $addon->Interest_Cycle,
                        );

                        $dailyRepaymentAmount += $addonTotal['daily_repayment'];
                    }
                }
            }
            $collectionFees = [];
            $dailyFee = 0;
            // First Pass: Process all fees except those applicable on installments
            foreach ($fees as $fee) {

                $Value = $fee->Value;
                $Calculation_Method = $fee->Calculation_Method;
                $Applicable_On = $fee->Applicable_On;
                $Applicable_At = $fee->Applicable_At;
                if ($Applicable_At == 'Application') {
                    $feeAmount = $Calculation_Method == 'Flat' ? $Value : ($fee->Value / 100) * $loanAmount;
                    $downPaymentSummary[] = ['name' => $fee->Name, 'percentage' => $fee->Value . ($Calculation_Method == 'Percentage' ? '%' : ''), 'amount' => 'UGX ' . number_format($feeAmount)];
                    if ($fee->Applicable_On == 'Principal') {
                        $lmsUssdSessionTracking->Arrangement_Fee += $feeAmount;
                    }
                    $total_Downpayment += $feeAmount;
                    $facilitationFees += $feeAmount;
                    continue; // Skip to next fee
                }
                if ($Applicable_At == 'Disbursement') {
                    $feeAmount = $Calculation_Method == 'Flat' ? $Value : ($fee->Value / 100) * $loanAmount;
                    $lmsUssdSessionTracking->Arrangement_Fee += $feeAmount;
                    $facilitationFees += $feeAmount;
                } else if ($Applicable_At == 'Repayment') {
                    if ($Calculation_Method == "Percentage") {
                        if ($Applicable_On == "Principal") {
                            $principal = $loanAmount;
                            $feeAmount = ($principal * $Value / 100);
                        } else if ($Applicable_On == "Interest") {
                            $interest = $loanProductTerm->Interest_Rate;
                            $feeAmount = ($interest * $Value / 100);
                        } else if ($Applicable_On == "Balance") {
                            $principal = $loanAmount;
                            $interest = $loanProductTerm->Interest_Rate;
                            $balance = $principal + $interest;
                            $feeAmount = ($balance * $Value / 100);
                        } else if ($Applicable_On == 'Installment Balance') {
                            $collectionFees[] = $fee;
                            continue;
                        }
                    } else {
                        $feeAmount = $fee->Value;
                    }
                    $dailyFee = $feeAmount;
                    $dailyRepaymentAmount += $dailyFee;
                    $feeAmount = $feeAmount * $numberOfPayments;
                    $settlementFee += $feeAmount;
                }
                $feesStructure[] = ['name' => $fee->Name, 'amount' => 'UGX ' . number_format($feeAmount), 'applicable_at' => $Applicable_At, 'dailyFee' => $dailyFee];
            }

            foreach ($collectionFees as $fee) {
                $Value = $fee->Value;
                $Applicable_At = $fee->Applicable_At;
                $feeAmount = $dailyRepaymentAmount * ($Value / 100);
                $dailyFee = $feeAmount;
                $feeAmount = $feeAmount * $numberOfPayments;
                $feesStructure[] = ['name' => $fee->Name, 'amount' => 'UGX ' . number_format($feeAmount), 'applicable_at' => $Applicable_At, 'dailyFee' => $dailyFee];
                $dailyRepaymentAmount += $dailyFee;
                $settlementFee += $feeAmount;
            }
            $totalRepaymentAmount += $settlementFee;
            $lmsUssdSessionTracking->Down_Payment = $total_Downpayment;
            $lmsUssdSessionTracking->save();
            $data = [
                'Credit_Amount_Approved' => $loanAmount,
                'Amount' => $loanAmount,
            ];

            // Create a new loan application
            $loan_application = LoanApplication::updateOrCreate([
                'Request_ID' => $requestId,
                'Partner_ID' => $partner->id,
                'Customer_ID' => $customer->id,
                'Loan_Product_ID' => $loanProduct->id,
            ], $data);

            DB::commit();
            $loanDuration = $loanProductTerm->Value > 30 ? round($loanProductTerm->Value / 30, 0) . ' Month(s)' : $loanProductTerm->Value . ' Days';
            return response()->json([
                "returnCode" => 0,
                "returnData" => [
                    "interestRate" => $loanProductTerm->Interest_Rate . '%',
                    "interestCycle" => $loanProductTerm->Interest_Cycle,
                    "frequencyOfInstallmentRepayment" => $frequencyOfInstallmentRepayment,
                    "loanDuration" => $loanDuration,
                    "repaymentAmount" => round($dailyRepaymentAmount),
                    "totalRepaymentAmount" => round($totalRepaymentAmount),
                    "requestId" => $requestId,
                    "payOff" => round($totalRepaymentAmount + $totalAddonsAmount), // removed $totalFacilitationFees because of ABC
                    "facilitationFee" => round($facilitationFees),
                    "loanAmount" => $loanAmount,
                    "settlementFee" => round($settlementFee),
                    "downPaymentSummary" => $downPaymentSummary,
                    "downPaymentTotal" => round($total_Downpayment),
                    "feesStructure" => $feesStructure,
                    "addons" => $addons,
                    "numberOfPayments" => $numberOfPayments,
                ],
                "returnMessage" => "Successfully posted loan application"
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            DB::rollBack();
            Log::error($validationException->getMessage());
            Log::error($validationException->getFile());
            Log::error($validationException->getLine());
            return response()->json([
                "returnCode" => 2,
                "returnMessage" => "Validation error",
                "errors" => $validationException->validator->errors()
            ], 200);
        } catch (\Throwable $throwable) {
            DB::rollBack();
            Log::error($throwable->getMessage());
            Log::error($throwable->getFile());
            Log::error($throwable->getLine());
            return response()->json([
                "returnCode" => 21,
                "returnMessage" => "An unexpected error occurred",
                "error" => $throwable->getMessage()
            ], 200);
        }
    }

    /**
     *
     * Create the loan application record and send to Yo for processing
     * @param \Illuminate\Http\Request $request
     * @throws \Exception
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function postApprovedLoan(Request $request)
    {
        try {
            $request->validate([
                "requestId" => "required",
                "phoneNumber" => "required",
                "loanStatus" => "required|in:Approved",
                "payOff" => "required",
            ]);

            $loan = null;

            $requestId = $request->requestId;

            $phoneNumber = $request->phoneNumber;
            $requestId = $request->requestId;
            $payOff = $request->payOff;
            $groupId = $request->groupId;
            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();
            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }

            $floatAmount = Account::where('partner_id', $partner->id)
                ->where('slug', AccountSeederService::DISBURSEMENT_OVA_SLUG)
                ->first()
                ->balance;
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $loan = $customer->loans()
                ->whereNot('Credit_Account_Status', 4) // Fully Paid
                ->whereNot('Credit_Account_Status', 3) // Written-off
                ->latest()
                ->first();

            if ($loan) {
                throw new Exception('Customer already has an active loan with ' . $loan->partner->Institution_Name . ' of UGX ' . number_format($loan->totalOutstandingBalance()) . ', Loan ID: ' . $loan->id, 400);
            }

            $loanApplicationSession = LmsUssdSessionTracking::where('requestId', $requestId)->first();

            if (!$loanApplicationSession) {
                throw new Exception('Loan application session not found.', 400);
            }


            $loanProduct = $loanApplicationSession->loanProduct;
            $loanApplication = $loanApplicationSession->loanApplication;
            $isAssetLoan = $loanProduct->loan_product_type->Code == "0";

            if ($floatAmount < $payOff && !$isAssetLoan) {
                throw new Exception("Not enough float to disburse your loan. Please try again later.", 400);
            }
            if ($isAssetLoan) {
                try {
                    // Initiate the Downpayment to customers phone
                    LoanService::initiateDownpayment($partner, $customer, $loanApplicationSession->Down_Payment, $loanApplicationSession->loanApplication);
                    $message = "Please pay the down payment to complete";
                    return response()->json([
                        "returnCode" => 0,
                        "returnMessage" => $message
                    ], 200);
                    // Once down payment is successful in callback or cron, create the loan, create the disbursement & schedules
                } catch (Exception $e) {
                    DB::rollBack();
                    $message = $e->getMessage();
                    return response()->json([
                        "returnCode" => 0,
                        "returnMessage" => $message
                    ], 200);
                }
            }
            // Check of the loan product type is Cash or Asset loan
            // Cash Loan
            if (!$isAssetLoan) {
                // Any Pending CashLoan Pipeline to be processed
                // ---------------------------------
                // Send the money to the customer.
                // API CALL
                // ---------------------------------
                if (!$groupId) {
                    if ($loanApplicationSession->Arrangement_Fee) {
                        $payOff -= $loanApplicationSession->Arrangement_Fee;
                    }

                    $disbursementSucceeded = LoanService::initiateDisbursement($partner, $customer, $payOff, $loanApplication->id);
                    if (!$disbursementSucceeded) {
                        $loanApplication->update([
                            'Rejection_Reason' => 'Temporary system failure',
                            'Rejection_Date' => date('Y-m-d'),
                            'Credit_Application_Status' => 'Rejected',
                        ]);
                        throw new Exception('Temporary system failure, please try again', 400);
                    }
                } else {
                    $this->depositGroupLoan($payOff, $groupId, $customer, $partner);
                }

                // The CheckYo Command will check for successful transaction and create the loan accordingly.
            }

            // Do all the above only after the external request has passed or the DB::transaction also takes care of
            // this issues by only committing the changes if the external request is successful.
            // if ($isAssetLoan) {
            //     $message = "Asset loan application approved. Please check your phone for the Downpayment.";
            // }
            return response()->json([
                "returnCode" => 0,
                "returnMessage" => "Loan is being processed, please wait."
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            Log::error($validationException->getMessage());
            Log::error($validationException->getFile());
            Log::error($validationException->getLine());

            return response()->json([
                "returnCode" => 2,
                "returnMessage" => "Validation error",
                "errors" => $validationException->validator->errors()
            ], 200);
        } catch (\Throwable $throwable) {
            Log::error($throwable->getMessage());
            Log::error($throwable->getFile());
            Log::error($throwable->getLine());

            return response()->json([
                "returnCode" => 21,
                "returnMessage" => "An unexpected error occurred",
                "error" => $throwable->getMessage()
            ], 200);
        }
    }

    public function getLoanBalance(Request $request)
    {
        try {
            $request->validate([
                "phoneNumber" => "required",
            ]);

            $phoneNumber = $request->phoneNumber;
            $checking = $request->checking;
            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();
            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                return response()->json([
                    "returnCode" => 41,
                    "returnMessage" => "An unexpected error occurred",
                    "error" => 'Customer account not found.'
                ], 200);
            }
            if ($checking == true) {
                $pendingApplication = $customer->loanApplications()->where('Credit_Application_Status', 'Pending')
                    ->whereHas('successfullDownPayment')
                    ->first();

                if ($pendingApplication) {
                    return response()->json([
                        "returnCode" => 21,
                        "returnMessage" => "An unexpected error occurred",
                        "returnMessage" => "Customer has an incomplete " . $pendingApplication->loan_product->Name . " loan application from " . $pendingApplication->partner->Institution_Name,
                    ], 200);
                }
            }
            // todo: Enable showing balance for a written off loan. This would only be the principal in arrears.
            $loan = $customer->loans()
                ->whereNot('Credit_Account_Status', 4) // Fully Paid
                ->whereNot('Credit_Account_Status', 3) // Written-off
                ->latest()
                ->first();

            if (!$loan) {
                return response()->json([
                    "returnCode" => 41,
                    "returnMessage" => "An unexpected error occurred",
                    "error" => 'Customer doesn\'t have an active loan.'
                ], 200);
            }

            return response()->json([
                "returnCode" => 0,
                "returnData" => [
                    "totalOutstanding" => round($loan->totalOutstandingBalance()),
                    "interestDue" => round($loan->interestDue()),
                    "principalDue" => round($loan->getOutstandingPrincipal()),
                    "penaltyDue" => round($loan->getOutstandingPenalties()),
                    "intallmentFrequency" => $loan->Credit_Payment_Frequency,
                    "Installment_Amount" => round($loan->getDailyRepayment()),
                    "remainingDays" => $loan->getOutstandingDays(), // this has to be revised
                    "totalDays" => round($loan->Number_of_Payments),
                    'fees' => round($loan->feesDue()),
                    "totalAmountDue" => round($loan->getAmountDue()),
                    "totalPaid" => round($loan->totalRepayment()), // this should be amount in arrears
                    "partnerName" => $loan->partner->Institution_Name,
                    "productName" => $loan->loan_product->Name,
                    "canRestructure" => boolval($loan->Can_Restructure),
                    "clearLoanAmount" =>  round($loan->totalOutstandingBalance()),
                ],
                "returnMessage" => "Outstanding Loan Balance breakdown"
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            Log::error($validationException->getMessage());
            Log::error($validationException->getFile());
            Log::error($validationException->getLine());
            return response()->json([
                "returnCode" => 4,
                "returnMessage" => "Validation error",
                "errors" => $validationException->validator->errors()
            ], 200);
        } catch (\Throwable $throwable) {
            Log::error($throwable->getMessage());
            Log::error($throwable->getFile());
            Log::error($throwable->getLine());
            return response()->json([
                "returnCode" => 41,
                "returnMessage" => "An unexpected error occurred",
                "error" => $throwable->getMessage()
            ], 200);
        }
    }

    public function postLoanRepayment(Request $request)
    {
        try {
            $request->validate([
                "phoneNumber" => "required",
                "amountPaid" => "required",
            ]);

            $amountPaid = $request->amountPaid;
            $phoneNumber = $request->phoneNumber;

            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();
            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            /**
             * todo: Accommodate paying for a Written loan
             * todo: Affect GL accounts: increase collection ova by amount received. Also increase Other Income account by same amount - Recoveries From Written Off Loans
             * todo: When loan is written off, clear the interest in the loan schedule. See WriteOffLoanAction
             */
            $loan = $customer->loans()
                ->whereNot('Credit_Account_Status', 4) // Fully Paid
                //->whereNot('Credit_Account_Status', 3) // Written-off
                ->latest()
                ->first();

            if (!$loan) {
                throw new Exception('Customer doesn\'t have an active loan.', 400);
            }

            //            if ($loan->Status == Loan::WRITTEN_OFF_STATUS) {
            //                throw new Exception('You cannot make a repayment on a written off loan.', 400);
            //            }

            $outstanding_balance = round($loan->totalOutstandingBalance());

            if ($loan->isCleared()) {
                throw new Exception("You cannot make a repayment on a cleared loan. You don't have any active loan.", 400);
            }

            if ($amountPaid > $outstanding_balance) {
                throw new Exception("You cannot make a repayment greater than the outstanding balance of UGX $outstanding_balance.", 400);
            }

            // TODO MA
            // Dispatch Job to Airtel
            $partner = $loan->partner;
            $customer = $loan->customer;
            LoanService::initiatRepayment($partner, $customer, $amountPaid, $loan);

            return response()->json([
                "returnCode" => 511,
                // TODO MA
                // We dipatch this in a job so nothing about Airtel is returned.
                // Let me know if we need to send the request inline
                // "returnData" => [
                //     "data" => ["transaction" => ["id" => "79", "status" => "Success."]],
                //     "status" => [
                //         "code" => "200",
                //         "message" => "Success.",
                //         "response_code" => "DP00800001006",
                //         "result_code" => "ESB000010",
                //         "success" => true
                //     ]
                // ],
                "returnMessage" => "Transaction In Progress."
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            Log::error($validationException->getMessage());
            Log::error($validationException->getFile());
            Log::error($validationException->getLine());
            return response()->json([
                "returnCode" => 5,
                "returnMessage" => "Validation error",
                "errors" => $validationException->validator->errors()
            ], 200);
        } catch (\Throwable $throwable) {
            Log::error($throwable->getMessage());
            Log::error($throwable->getFile());
            Log::error($throwable->getLine());
            return response()->json([
                "returnCode" => 51,
                "returnMessage" => "An unexpected error occurred",
                "error" => $throwable->getMessage()
            ], 200);
        }
    }

    /**
     * Returns the all of the customers loans contrary to what the API name says.
     * This is done to maintain the consistency of the API with SCL - Service cops
     * @param \Illuminate\Http\Request $request
     * @throws \Exception
     * @return mixed|\Illuminate\Http\JsonResponse
     */
    public function miniStatement(Request $request)
    {
        try {
            $request->validate([
                "phoneNumber" => "required",
            ]);

            $phoneNumber = $request->phoneNumber;

            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();
            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $loans = $customer->loans()
                // ->whereNot('Credit_Account_Status', Loan::ACCOUNT_STATUS_WRITTEN_OFF)
                ->where('Partner_ID', $partner->id)
                ->latest()
                ->get();

            if (count($loans) == 0) {
                throw new ExpectedException('Mini-statement not found.', 62);
            }

            $customerLoans = CustomerLoansApiResource::collection($loans);

            return response()->json([
                "returnCode" => 0,
                "returnData" => $customerLoans,
                "returnMessage" => "Found"
            ], 200);
        } catch (ExpectedException $exception) {
            return response()->json([
                "returnCode" => $exception->getCode(),
                "returnMessage" => $exception->getMessage(),
                "error" => $exception->getMessage()
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            Log::error($validationException->getMessage());
            Log::error($validationException->getFile());
            Log::error($validationException->getLine());
            return response()->json([
                "returnCode" => 500,
                "returnMessage" => "Validation error",
                "errors" => $validationException->validator->errors()
            ], 200);
        } catch (\Throwable $throwable) {
            Log::error($throwable->getMessage());
            Log::error($throwable->getFile());
            Log::error($throwable->getLine());
            return response()->json([
                "returnCode" => 500,
                "returnMessage" => "An unexpected error occurred",
                "error" => $throwable->getMessage()
            ], 200);
        }
    }

    public function miniStatementDetails(Request $request)
    {
        try {
            $request->validate([
                "phoneNumber" => "required",
                "loanAccount" => "required",
            ]);

            $loanAccount = $request->loanAccount;
            $phoneNumber = $request->phoneNumber;

            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();
            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $loan = Loan::where("Credit_Account_Reference", $loanAccount)->first();

            if (!$loan) {
                throw new ExpectedException('Customer loan doesn\'t exist.', 41);
            }

            return response()->json([
                "returnCode" => 0,
                "returnData" => [
                    "end_date" => $loan->Maturity_Date,
                    "term_value" => round($loan->term->Value / 30),
                    "balance" => round($loan->totalOutstandingBalance()),
                    "penalties" => round($loan->getOutstandingPenalties()),
                    "loan_account" => $loan->Credit_Account_Reference,
                    "paid" => round($loan->totalPayment()),
                    "term_code" => "Month(s)",
                    "loan_amount" => $loan->Credit_Amount,
                    "daily_interest" => $loan->term->Interest_Rate / 365,
                    "start_date" => $loan->Credit_Account_Date,
                    "fees" => round($loan->getOutstandingFees()),
                    "interest_amount" => round($loan->getOutstandingInterest())
                ],
                "returnMessage" => "Found"
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            Log::error($validationException->getMessage());
            Log::error($validationException->getFile());
            Log::error($validationException->getLine());
            return response()->json([
                "returnCode" => 500,
                "returnMessage" => "Validation error",
                "errors" => $validationException->validator->errors()
            ], 200);
        } catch (\Throwable $throwable) {
            Log::error($throwable->getMessage());
            Log::error($throwable->getFile());
            Log::error($throwable->getLine());
            return response()->json([
                "returnCode" => 500,
                "returnMessage" => "An unexpected error occurred",
                "error" => $throwable->getMessage()
            ], 200);
        }
    }

    public function loanStatusCheck(Request $request)
    {
        try {
            $request->validate([
                "requestId" => "required",
            ]);

            $requestId = $request->requestId;

            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }

            $loanApplicationSession = LmsUssdSessionTracking::where('requestId', $requestId)->first();
            $loan = $loanApplicationSession->loan;
            if (!$loan) {
                throw new Exception('Loan not found.', 400);
            }
            $customer = $loanApplicationSession->customer;
            if (!$customer) {
                throw new Exception('Customer not found.', 400);
            }

            return response()->json([
                "returnCode" => 0,
                "returnData" => [
                    "loan_status" => $loan->Status,
                    "phone_no" => $customer->Telephone_Number,
                    "principal_amount" => $loan->totalOutstandingBalance(),
                    "loan_acct_no" => $loan->Credit_Account_Reference,
                ],
                "returnMessage" => "Found"
            ], 200);
        } catch (\Illuminate\Validation\ValidationException $validationException) {
            Log::error($validationException->getMessage());
            Log::error($validationException->getFile());
            Log::error($validationException->getLine());
            return response()->json([
                "returnCode" => 500,
                "returnMessage" => "Validation error",
                "errors" => $validationException->validator->errors()
            ], 200);
        } catch (\Throwable $throwable) {
            Log::error($throwable->getMessage());
            Log::error($throwable->getFile());
            Log::error($throwable->getLine());
            return response()->json([
                "returnCode" => 500,
                "returnMessage" => "An unexpected error occurred",
                "error" => $throwable->getMessage()
            ], 200);
        }
    }

    public function getRestructureDetails(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                "phoneNumber" => "required",
                "days" => "required|integer|min:1",
            ]);

            // Extract inputs
            $days = $request->days;
            $phoneNumber = $request->phoneNumber;
            $partnerCode = $request->header('X-PARTNER-CODE');

            // Fetch partner and customer
            $partner = Partner::where('Identification_Code', $partnerCode)->first();
            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            // Validate partner and customer
            if (!$partner) {
                throw new Exception('Partner account not found. You provided an invalid partner code in the header.', 400);
            }
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            // Fetch the active loan
            $loan = $customer->loans()
                ->whereNotIn('Credit_Account_Status', [3, 4]) // Exclude Written-off and Fully Paid
                ->latest()
                ->first();

            if (!$loan) {
                throw new Exception('Customer doesn\'t have an active loan.', 400);
            }

            if ($loan->Status == Loan::WRITTEN_OFF_STATUS) {
                throw new Exception('You cannot make a repayment on a written-off loan.', 400);
            }

            if (!$loan->Can_Restructure) {
                throw new Exception('Restructuring is not enabled for this loan.', 400);
            }

            // Fetch the penalty fee
            $penaltyFee = $loan->loan_product->penalties()->first();
            if (!$penaltyFee) {
                throw new Exception('Product does not have a penalty fee.', 400);
            }

            // Step 1: Get amount due (arrears)
            $arrears = $loan->getAmountDue();
            // $arrears = 105000;
            // Step 2: Calculate total restructured amount (arrears + penalty)
            $dailypenaltyRate = ($penaltyFee->Value / 100) / 30; // Penalty rate (e.g., 3.5% / 30)

            $penaltyRate = $dailypenaltyRate * $days;

            $penalty = $arrears * $penaltyRate;
            $totalRestructuredAmount = $arrears + $penalty;

            $totalUpfrontPayment = 0;
            if ($days == 3) {
                $totalUpfrontPayment = 0.2 * $totalRestructuredAmount;
            }
            if ($days == 5) {
                $totalUpfrontPayment = 0.3 * $totalRestructuredAmount;
            }
            if ($days == 7) {
                $totalUpfrontPayment = 0.5 * $totalRestructuredAmount;
            }
            $totalRestructuredAmount -= $totalUpfrontPayment;

            // Step 3: Use $days to calculate the restructured amount per installment
            $additionalAmountPerInstallment = $totalRestructuredAmount / $days;
            // Step 4: Fetch the existing repayment schedule starting from today
            $existingSchedule = $loan->schedule()
                ->where('total_outstanding', '>', 0) // Only outstanding payments
                ->whereBetween('payment_due_date', [
                    now()->addDay()->startOfDay()->toDateTimeString(),
                    now()->addDays($days)->endOfDay()->toDateTimeString()
                ]) // Start from today
                ->orderBy('payment_due_date') // Order by due date
                ->get();

            if ($existingSchedule->isEmpty()) {
                throw new Exception("No repayments with outstanding amounts found starting from today.");
            }

            // Step 5: Group the schedule by installment_number
            $groupedSchedule = $existingSchedule->groupBy('installment_number');

            // Step 6: Calculate the new installment amount per grouped schedule
            $newInstallmentAmount = 0;
            foreach ($groupedSchedule as $installmentNumber => $installmentPayments) {
                // Calculate the total payment for the installment (original + additional)
                $newInstallmentAmount = $installmentPayments->sum('total_payment') + $additionalAmountPerInstallment;
                break; // We only need the amount for one installment
            }
            return response()->json([
                "returnCode" => 0,
                "returnData" => [
                    'restructureAmount' => round($totalRestructuredAmount),
                    'restructurePenalty' => round($penalty),
                    'restructurePayment' => round($totalUpfrontPayment),
                    'restructureTermInDays' => round($days), // Assuming whole number
                    'restructureRepaymentFrequency' => 'Daily', // Assuming daily repayments
                    'additionalAmountPerInstallment' => round($additionalAmountPerInstallment),
                    'newInstallmentAmount' => round($newInstallmentAmount), // Amount per grouped schedule
                ],
                "returnMessage" => "Successful"
            ], 200);
            // Return the restructure details

        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json([
                "returnCode" => 500,
                "returnMessage" => $e->getMessage()
            ], 200);
        }
    }

    public function requestRepaymentConfirmation(Request $request)
    {
        // Validate the request
        $request->validate([
            "confirm" => "required|boolean", // User confirms by sending true
        ]);

        // Return true if the user confirms
        return $request->confirm;
    }

    public function restructureLoan(Request $request)
    {
        $phoneNumber = $request->phoneNumber;
        $payOff = $request->payOff;
        $days = $request->days;
        try {
            if (!$phoneNumber || !$payOff || !$days) {
                throw new Exception('Required fields (payOff / days) not found.', 400);
            }
            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();
            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }
            $loan = $customer->loans()
                ->whereNotIn('Credit_Account_Status', [3, 4]) // Exclude Written-off and Fully Paid
                ->latest()
                ->first();

            if (!$loan) {
                throw new Exception('Customer doesn\'t have an active loan.', 400);
            }

            if ($loan->Status == Loan::WRITTEN_OFF_STATUS) {
                throw new Exception('You cannot make a repayment on a written-off loan.', 400);
            }
            // Initiate the Downpayment to customers phone
            LoanService::initiateRestructure($payOff, $loan, $days);
            DB::commit();
            $message = "Please pay the provided amount to complete";
            return response()->json([
                "returnCode" => 0,
                "returnMessage" => $message
            ], 200);
            // Once downpayment is successful in callback or cron, create the loan, create the disbursment & schedules
        } catch (Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            $message = $e->getMessage();
            return response()->json([
                "returnCode" => 0,
                "returnMessage" => $message
            ], 200);
        }
    }

    public function depositGroupLoan($amount, $groupId, $customer, $partner)
    {
        $url = env('APP_ENV') == 'local' ? env('DAS_URL_LOCAL') : env('DAS_URL_PROD');
        $url = $url . '/groups/loan-applications/handleDisbursement';
        $telcoTxnId = Transaction::generateID();
        $payload = [
            'amount' => $amount,
            'group_id' => $groupId,
            'telco_txn_id' => $telcoTxnId,
        ];

        try {
            $response = Http::post($url, $payload);
            if ($response->successful()) {
                Transaction::create([
                    'Partner_ID' => $partner->id,
                    'Type' => Transaction::DISBURSEMENT,
                    'Amount' => $amount,
                    'Status' => 'SUCCEEDED',
                    'Telephone_Number' => $customer->Telephone_Number,
                    'TXN_ID' => $telcoTxnId,
                ]);
                return $response->json();
            } else {
                throw new Exception($response->body());
            }
        } catch (Exception $e) {
            Log::error($e->getMessage());
            throw new Exception($e->getMessage());
        }
    }

    public function getProductDetails(Request $request)
    {
        try {
            // Validate the request data
            $validator = Validator::make($request->all(), [
                'productCode' => 'required|exists:savings_products,code',
                'score' => 'required|string',
                'age' => 'required|numeric',
                'isBoda' => 'required|boolean',
                'type' => 'required|string',
                'phoneNumber' => 'required',
            ]);

            // If validation fails, return error response
            if ($validator->fails()) {
                $message = $validator->errors()->first();
                throw new Exception($message);
            }
            $phoneNumber = $request->phoneNumber;
            $productCode = $request->productCode;
            $score = $request->score;
            $age = $request->age;
            $isBoda = $request->isBoda;
            $type = $request->type;
            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->firstOrFail();
            $product = SavingsProduct::where('code', $productCode)->where('Partner_ID', $partner->id)->firstOrFail();
            $customer = Customer::where('Telephone_Number', $phoneNumber)->firstOrFail();

            // Fetch all saving product periods
            $customerSavingsPreferences = CustomerSavingsPreferences::where('Saving_Product_ID', $product->id)
                ->where('Customer_ID', $customer->id)
                ->where('Payment_Type', $type)
                ->select('Provider_Name', 'Payment_Frequency', 'Default_Installment', 'Payment_Type')->first();
            $savingProductFees = SavingsProductFee::where('savings_product_id', $product->id)->select('id', 'value', 'minimum_amount', 'maximum_amount')->get();
            //            $providers = SavingsProvider::where('Score', $score)
            //                ->orWhere(function ($query) use ($age) {
            //                    $query->where('Minimum_Age', '<=', $age)
            //                        ->where('Maximum_Age', '>=', $age);
            //                })
            //                ->when(in_array($isBoda, [true, false, 0, 1], true), function ($query) use ($isBoda) {
            //                    $query->orWhere(function ($query) use ($isBoda) {
            //                        $query->whereNotNull('isBoda')
            //                            ->where('isBoda', $isBoda === true ? 1 : ($isBoda === false ? 0 : $isBoda));
            //                    });
            //                })
            //                ->select('Provider_Name', 'Interest_Rate', 'Down_Payment')
            //                ->get();

            //$isBoda = (int) $isBoda;

            $providers = SavingsProvider::query()->select('Provider_Name', 'Interest_Rate', 'Down_Payment', 'Locations')
                ->whereRaw('((Minimum_Age <= ? and ? <= Maximum_Age) or (Score is not null and Score = ?) or (isBoda is not null and isBoda = ?)) and Saving_Product_ID = ?', [
                    $age,
                    $age,
                    $score,
                    $isBoda,
                    $product->id
                ])->get();


            $assetLocations = AssetLocation::select('name')->pluck('name')->toArray();

            foreach ($providers as $provider) {
                $totalPayment = $product->cost * (1 + ($provider->Interest_Rate) / 100);
                $provider->total_Loan_Amount = $totalPayment;
                $provider->Locations = json_decode($provider->Locations);
            }
            $account = $customer->savings_account()->where('savings_product_id', $product->id)->first();
            $paidAmount = $account->current_balance ?? 0;
            $expectedAmount = $account->expected_amount ?? null;
            if ($expectedAmount) {
                $balance = $expectedAmount - $paidAmount;
            } else {
                $balance = null;
            }
            // if ($paidAmount > 0) {
            //     $minimumDeposit = 10000;
            // } else {
            $minimumDeposit = $product->minimum_deposit;
            // }
            // Return the data as a JSON response
            return response()->json([
                "returnCode" => 0,
                'returnMessage' => 'Saving product details retrieved successfully',
                'returnData' => [
                    'productName' => $product->name,
                    'productCost' => $product->cost,
                    'minimumDeposit' => $minimumDeposit,
                    'productFees' => $savingProductFees,
                    'providers' => $providers,
                    'customerPreferences' => $customerSavingsPreferences,
                    'paid' => round($paidAmount),
                    'balance' => $balance,
                    'locations' => $assetLocations,
                ],
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                "returnCode" => 2,
                "returnMessage" => $e->getMessage(),
            ], 200);
        }
    }

    public function postCustomerPeriod(Request $request)
    {
        try {
            // Validate the request data
            $validator = Validator::make($request->all(), [
                'phoneNumber' => 'required',
                'period' => 'required|exists:saving_product_periods,id',
            ]);

            // If validation fails, return error response
            if ($validator->fails()) {
                $message = $validator->errors()->first();
                throw new Exception($message);
            }
            $phoneNumber = $request->phoneNumber;
            $period = $request->period;

            $partnerCode = $request->header('X-PARTNER-CODE');
            $partner = Partner::where('Identification_Code', $partnerCode)->first();

            if (!$partner) {
                throw new Exception('Partner account not found. You provided an valid partner code in the header.', 400);
            }

            $customer = Customer::where('Telephone_Number', $phoneNumber)->first();

            if (!$customer) {
                throw new Exception('Customer account not found.', 400);
            }

            $savingProductPeriod = SavingProductPeriod::find($period);

            if (!$savingProductPeriod) {
                throw new Exception('Saving product period not found.', 400);
            }

            // Create a new CustomerSavingPeriod record
            CustomerSavingPeriod::create([
                'Partner_ID' => $partner->id,
                'Customer_ID' => $customer->id,
                'Product_Period_ID' => $savingProductPeriod->id,
            ]);

            // Return success response
            return response()->json([
                "returnCode" => 0,
                "returnMessage" => 'Customer period added successfully',
            ], 200);
        } catch (Exception $e) {
            return response()->json([
                "returnCode" => 2,
                "returnMessage" => $e->getMessage(),
            ], 200);
        }
    }

    public function optOut(Request $request, OptOutCustomerAction $action)
    {
        try {
            // Validate the request data
            $data = $request->only('phoneNumber');
            $data['partnerCode'] = $request->header('X-PARTNER-CODE');
            $validator = Validator::make($data, [
                'phoneNumber' => [
                    'required',
                    'exists:customers,Telephone_Number',
                ],
                'partnerCode' => [
                    'required',
                    'exists:partners,Identification_Code',
                ]
            ]);

            // If validation fails, return error response
            if ($validator->fails()) {
                $message = $validator->errors()->first();

                throw new Exception($message);
            }

            $action->execute($request->phoneNumber, $request->header('X-PARTNER-CODE'));

            return response()->json([
                "returnCode" => 0,
                "returnMessage" => 'Opt-out successful',
            ]);
        } catch (\Exception $exception) {
            return response()->json([
                "returnCode" => 2,
                "returnMessage" => $exception->getMessage(),
            ]);
        }
    }

    public function storeCreditScore($creditData)
    {
        CreditScore::create($creditData);
    }
}
