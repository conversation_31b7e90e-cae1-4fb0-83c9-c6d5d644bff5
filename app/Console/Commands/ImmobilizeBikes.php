<?php

namespace App\Console\Commands;

use App\Actions\ImmobilizeBikeAction;
use App\Jobs\ImmobilizeBikeJob;
use App\Models\Customer;
use App\Models\CustomerAsset;
use App\Services\LoanRepaymentReminderService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class ImmobilizeBikes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:immobilize {--bike=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Immobilize bikes...';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        if ($this->option('bike')) {
            $asset = CustomerAsset::query()->where('Identification', $this->option('bike'))
                ->whereNull('Disabled_At')->whereHas('loan.latestOutstandingPayment', function (Builder $query) {
                    return $query->whereBeforeToday('payment_due_date');
                })->first();
            if (empty($asset)) {
                logger()->error('Bike ' . $this->option('bike') . ' not found or is not disabled or has no outstanding payment');

                return 1;
            }
            ImmobilizeBikeJob::dispatch($asset);

            return 0;
        }

        // Find bikes that belong to loan defaulters and have not been already immobilized.
        CustomerAsset::query()
            ->whereNull('Disabled_At')
            ->whereHas('loan.latestOutstandingPayment', function (Builder $query) {
                return $query->whereBeforeToday('payment_due_date');
            })->each(function (CustomerAsset $asset) {
                ImmobilizeBikeJob::dispatch($asset);
            });

        return 0;
    }
}
