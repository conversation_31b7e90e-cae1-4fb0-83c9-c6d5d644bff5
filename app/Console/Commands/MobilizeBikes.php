<?php

namespace App\Console\Commands;

use App\Actions\ImmobilizeBikeAction;
use App\Jobs\ImmobilizeBikeJob;
use App\Jobs\MobilizeBikeJob;
use App\Models\Customer;
use App\Models\CustomerAsset;
use App\Services\LoanRepaymentReminderService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class MobilizeBikes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:mobilize {--bike=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Mobilize bikes...';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        if ($this->option('bike')) {
            $asset = Customer::query()->firstWhere('Identification', $this->option('bike'));

            if (empty($asset)) {
                logger()->error('Bike ' . $this->option('bike') . ' not found');

                return 1;
            }

            MobilizeBikeJob::dispatch($asset);

            return 0;
        }

        // Find bikes that belong to loan defaulters.
        CustomerAsset::query()
            ->whereNotNull('Disabled_At')
            ->whereNotNull('Last_Disabled_at')
            ->whereDoesntHave('loan.latestOutstandingPayment', function (Builder $query) {
                $query->wherePast('payment_due_date');
                return $query->whereRaw('DATEDIFF(CURRENT_DATE, payment_due_date) > 1');
            })->each(function (CustomerAsset $asset) {
                // dump($asset->loan->latestOutstandingPayment->payment_due_date);
                //MobilizeBikeJob::dispatch($asset);
            });

        return 0;
    }
}
