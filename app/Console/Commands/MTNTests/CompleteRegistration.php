<?php

namespace App\Console\Commands\MTNTests;

use App\Models\Customer;
use App\Services\MtnApiService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Console\Command;
use Illuminate\Http\Client\ConnectionException;

class CompleteRegistration extends Command
{
    protected $signature = 'mtn:complete-registration {phone}';
    protected $description = 'Test MTN customer registration API';

    /**
     * @throws GuzzleException
     * @throws ConnectionException
     */
    public function handle(): int
    {
        $customer = Customer::firstWhere('Telephone_Number', $this->argument('phone'));

        if (!$customer) {
            $this->error('Customer not found');

            return 1;
        }

        $mtnService = new MtnApiService('test');

        if (! $mtnService->customerRegistrationCompleted($customer)) {
            $this->error('Customer registration completion failed');

            return 1;
        }

        $this->info('Customer registration completion successful');

        return 0;
    }
}
