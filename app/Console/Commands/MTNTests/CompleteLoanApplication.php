<?php

namespace App\Console\Commands\MTNTests;

use App\Models\Customer;
use App\Models\Transaction;
use App\Services\MtnApiService;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;
use Symfony\Component\Process\Exception\ProcessFailedException;

class CompleteLoanApplication extends Command
{
    protected $signature = 'mtn:complete-application {transactionId}';
    protected $description = 'Complete loan application';

    /**
     */
    public function handle(): int
    {
        $transaction = Transaction::query()->firstWhere('txn_id', $this->argument('transactionId'));

        if (empty($transaction)) {
            $this->error('Transaction not found');
            return 1;
        }

//        \App\Jobs\CompleteLoanApplication::dispatch($transaction);
//        $this->info('Job dispatched');
//        return 0;

        // Get MTN API configuration
        $mtnService = new MtnApiService('test');
        $mtnService->loanApplicationCompleted($transaction);

        $this->info('Request sent, check logs');

        return 0;
        $baseUrl = $mtnService->getBaseUrl();
        $username = $mtnService->getLoansUsername();
        $password = $mtnService->getLoansPassword();
        $certPath = storage_path($mtnService->getDfcuCertificate());
        $keyPath = storage_path($mtnService->getLmsPrivateKey());
        $caPath = storage_path($mtnService->getDfcuCaCertificate());

        // Load loan application data
        $loanApplicationSession = $transaction->loanApplication->loan_session;
        $loanApplicationSession->loadMissing('loanProductTerm');
        $loanProductTerm = $loanApplicationSession->loanProductTerm;

        // Create XML payload
        $payload = '<?xml version="1.0" encoding="UTF-8"?>' . PHP_EOL .
            '<p:loanapplicationcompletedrequest xmlns:p="http://www.ericsson.com/em/emm/sp/frontend" xmlns:op="http://www.ericsson.com/em/emm/common" xmlns:java="http://www.oracle.com/XSL/Transform/java" xmlns:tns="http://www.temenos.com/T24/event/RWFInbound/CusAsyncResp">' . PHP_EOL .
            '    <resource>FRI:' . $transaction->Telephone_Number . '@WKD-PERSONAL/SP</resource>' . PHP_EOL .
            '    <customerid>' . $transaction->Telephone_Number . '</customerid>' . PHP_EOL .
            '    <loanaccount>' . PHP_EOL .
            '        <accountnumber>' . $transaction->Loan_Application_ID . '</accountnumber>' . PHP_EOL .
            '        <status>APPROVED</status>' . PHP_EOL .
            '        <due>' . PHP_EOL .
            '            <amount>' . (int) $transaction->Amount . '</amount>' . PHP_EOL .
            '            <currency>UGX</currency>' . PHP_EOL .
            '        </due>' . PHP_EOL .
            '        <duedate>' . $loanApplicationSession->Maturity_Date->toDateString() . '</duedate>' . PHP_EOL .
            '        <tenure>' . $loanProductTerm->Value . '</tenure>' . PHP_EOL .
            '        <loantype>PERSONAL</loantype>' . PHP_EOL .
            '        <interest>0</interest>' . PHP_EOL .
            '    </loanaccount>' . PHP_EOL .
            '    <message>Hello, your loan request with Weekend Loan has been accepted. Please wait for the funds to be deposited to your MM account.</message>' . PHP_EOL .
            '</p:loanapplicationcompletedrequest>';

        // Save payload to a temporary file
//        $payloadFile = tempnam(sys_get_temp_dir(), 'mtn_payload_');
//        file_put_contents($payloadFile, $payload);

        // Build curl command
        $curlCommand = [
            'curl',
            '-u', "$username:$password",
            //'-v', // Verbose output
            '-k', // Skip SSL verification
            $baseUrl . '/sp/loanapplicationcompleted',
            '--cacert', $caPath,
            '--cert', $certPath,
            '--key', $keyPath,
            '-H', 'Content-Type: application/xml',
            '-H', 'Accept: application/xml',
            //'-H', 'Authorization: Basic ' . base64_encode("$username:$password"),
            '-d', $payload,

        ];

        //$this->info('curl: ' . json_encode($curlCommand));

        try {
            $result = Process::run($curlCommand);
            $this->info($result->command());

            // Process output
            if ($result->successful()) {
                $this->info('Request successful!');
                //$this->line('Response:');
                $this->line($result->output());

                // Log the response
                //Log::info('MTN LA Completed Response: ' . $output);

                // Clean up temp file
                //@unlink($payloadFile);

                return 0;
            } else {
                $this->error('Request failed!');
                //$this->error('Error output:');
                $this->error($result->errorOutput());

                // Log the error
                //Log::error('MTN LA Error: ' . $result->errorOutput());

                // Clean up temp file
                //@unlink($payloadFile);

                return 1;
            }
        } catch (ProcessFailedException $exception) {
            $this->error('Process failed: ' . $exception->getMessage());
            //Log::error('Process failed: ' . $exception->getMessage());

            // Clean up temp file
            //@unlink($payloadFile);

            return 1;
        }
    }
}
