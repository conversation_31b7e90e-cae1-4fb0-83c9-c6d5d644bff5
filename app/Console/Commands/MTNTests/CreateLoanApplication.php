<?php

namespace App\Console\Commands\MTNTests;

use App\Services\MockMtnApiService;
use App\Services\MtnApiService;
use Illuminate\Console\Command;

class CreateLoanApplication extends Command
{
    protected $signature = 'mtn:loan-application';

    protected $description = 'Test MTN loan application API';

    public function handle(): int
    {
        try {
            $msisdn = $this->ask('Enter customer phone number (format: 256XXXXXXXXX)');
            if (!preg_match('/^256[0-9]{9}$/', $msisdn)) {
                $this->error('Invalid phone number format. Must be in format: 256XXXXXXXXX');
                return 1;
            }

            $amount = (float) $this->ask('Enter loan amount');
            if ($amount <= 0) {
                $this->error('Amount must be greater than 0');
                return 1;
            }

            $tenor = (int) $this->ask('Enter loan tenor in days (1-30)');
            if ($tenor < 1 || $tenor > 30) {
                $this->error('Tenor must be between 1 and 30 days');
                return 1;
            }

            if (!$this->confirm("Are you sure you want to create a loan application for {$msisdn} with amount {$amount} UGX for {$tenor} days?")) {
                $this->info('Operation cancelled');
                return 0;
            }

            $this->info("Creating loan application...");
            $this->line("MSISDN: $msisdn");
            $this->line("Amount: $amount");
            $this->line("Tenor: $tenor days");

            $mtnService = new MockMtnApiService();
            $response = $mtnService->createLoanApplication($msisdn, $amount, $tenor);

            $this->info('Response:');
            $this->line(json_encode($response, JSON_PRETTY_PRINT));

            return 0;
        } catch (\Exception $e) {
            $this->error('Error:');
            $this->error($e->getMessage());
            if (config('services.mtn.debug')) {
                $this->error('Stack trace:');
                $this->error($e->getTraceAsString());
            }
            return 1;
        }
    }
} 