<?php

namespace App\Console\Commands\YoTests;

use App\Actions\CreateTestTransactionAction;
use App\Models\PartnerOva;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

/**
 * Use this when in UATs for Airtel
 */
class Disburse extends Command
{
  protected $signature = 'yo:disburse {--phone=} {--amount=} {--partner=}';
  protected $description = 'Collect money from customer use: 752600157 for testing';

  public function handle(): int
  {
    try {
      $transaction = app(CreateTestTransactionAction::class)->execute(
        $this->option('amount'),
        $this->option('phone'),
        $this->option('partner')
      );
      $id = Str::random(10);
      $api = (new PaymentServiceManager($transaction))->paymentService;

      $response = $api->disburse($this->option('phone'), $this->option('amount'), $id, $transaction->Type);

      $this->info('Response');
      $this->info(json_encode($response, true));
      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
