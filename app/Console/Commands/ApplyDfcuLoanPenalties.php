<?php

namespace App\Console\Commands;

use App\Models\Loan;
use App\Models\LoanPenalty;
use App\Models\Partner;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ApplyDfcuLoanPenalties extends Command
{
    private const PARTNER_CODE = 'CB009';
    private const PENALTY_HISTORY_FILE = 'penalty_history.csv';

    protected $signature = 'lms:apply-dfcu-loan-penalties';
    protected $description = 'Apply loan penalties for DFCU partner';

    public function handle()
    {
        try {
            $partner = $this->getPartner();
            if (!$partner) {
                return 1;
            }

            $this->processLoans($partner);
            $this->info('Loan penalties applied successfully.');
            return 0;
        } catch (Exception $e) {
            $this->logError($e);
            return 1;
        }
    }

    private function getPartner(): ?Partner
    {
        $this->info("Applying penalties for partner: " . self::PARTNER_CODE);
        $partner = Partner::where('Identification_Code', self::PARTNER_CODE)->first();

        if (!$partner) {
            $this->error("Partner with code " . self::PARTNER_CODE . " not found.");
            return null;
        }

        $this->info("Found partner: {$partner->Identification_Code} - {$partner->Name}");
        return $partner;
    }

    private function processLoans(Partner $partner): void
    {
        $loans = $this->getEligibleLoans($partner);

        foreach ($loans as $loan) {
            $this->processLoanPenalty($loan);
        }
    }

    private function getEligibleLoans(Partner $partner)
    {
        return Loan::where('Partner_ID', $partner->id)
            ->has('loan_product.penalties')
            ->whereIn('Credit_Account_Status', [Loan::ACCOUNT_STATUS_CURRENT_AND_WITHIN_TERMS])
            ->get();
    }

    private function processLoanPenalty(Loan $loan): void
    {
        $penalty = $loan->productPenalties()->first();
        $dailyPenaltyRate = $penalty->Value / 30 / 100;
        $outstandingDays = $this->calculateOutstandingDays($loan);
        if ($outstandingDays <= 0 || $outstandingDays > 14) {
            $this->info("Loan ID: {$loan->id} has no outstanding days or is not eligible for penalty application.");
            return;
        }

        $this->info("Processing Loan ID: {$loan->id} with outstanding days: {$outstandingDays}");
        $penaltyAmount = $loan->getAmountDue() * $dailyPenaltyRate * $outstandingDays;
        $penaltyAmount = round($penaltyAmount); // Ensure penalty amount is rounded to 2 decimal places
        if ($penaltyAmount <= 0) {
            $this->info("No penalty amount to apply for Loan ID: {$loan->id}");
            return;
        }
        $this->info("Calculated penalty amount for Loan ID: {$loan->id} is {$penaltyAmount}");
        $this->updateOrCreatePenalty($loan, $penalty, $penaltyAmount);
    }

    private function calculateOutstandingDays(Loan $loan): int
    {
        $outstandingDays = abs($loan->getOutstandingDays());
        return $outstandingDays;
    }

    private function updateOrCreatePenalty(Loan $loan, $penalty, float $penaltyAmount): void
    {
        $existingPenalty = LoanPenalty::where('Loan_ID', $loan->id)->first();

        if ($existingPenalty) {
            $this->updateExistingPenalty($existingPenalty, $penaltyAmount);
        } else {
            $this->createNewPenalty($loan, $penalty, $penaltyAmount);
        }

        $this->info("Applied penalty of {$penaltyAmount} to Loan ID: {$loan->id}");
    }

    private function updateExistingPenalty(LoanPenalty $penalty, float $amount): void
    {
        $penalty->update([
            'Amount_To_Pay' => $amount,
            'Status' => LoanPenalty::NOT_PAID,
        ]);
    }

    private function createNewPenalty(Loan $loan, $penalty, float $amount): void
    {
        LoanPenalty::create([
            'Partner_ID' => $loan->Partner_ID,
            'Loan_ID' => $loan->id,
            'Customer_ID' => $loan->Customer_ID,
            'Amount' => 0,
            'Amount_To_Pay' => $amount,
            'Product_Penalty_ID' => $penalty->id,
        ]);
    }

    private function logError(Exception $e): void
    {
        $this->error($e->getMessage());
        Log::error($e->getMessage());
    }
}
