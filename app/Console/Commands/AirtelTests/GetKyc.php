<?php

namespace App\Console\Commands\AirtelTests;

use App\Actions\CreateTestTransactionAction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;

class GetKyc extends Command
{
  protected $signature = 'airtel:kyc {--phone=} {--amount=} {--partner=}';
  protected $description = 'Get KYC details for a phone number. Currently only Airtel API calls.';

  public function handle(): int
  {
    $transaction = app(CreateTestTransactionAction::class)->execute(
      $this->option('amount') ?? 500,
      $this->option('phone'),
      $this->option('partner')
    );

    try {
      $api = (new PaymentServiceManager($transaction))->paymentService;
      $details = $api->kyc($this->option('phone'));

      logger()->info('KYC:', $details);

      dump($details);

      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
