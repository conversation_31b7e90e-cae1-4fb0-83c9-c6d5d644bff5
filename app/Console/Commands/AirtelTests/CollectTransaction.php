<?php

namespace App\Console\Commands\AirtelTests;

use App\Actions\CreateTestTransactionAction;
use App\Models\PartnerOva;
use App\Models\Transaction;
use App\Services\PaymentServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Number;
use Illuminate\Support\Str;

/**
 * Use this when in UATs for Airtel
 */
class CollectTransaction extends Command
{
  protected $signature = 'airtel:collect-transaction {transactionId}';
  protected $description = 'Collect money from customer by the transaction id';

  public function handle(): int
  {
    try {
      $transaction = Transaction::query()->firstWhere('TXN_ID', $this->argument('transactionId'));
      $api = (new PaymentServiceManager($transaction))->paymentService;
      $response = $api->collect($transaction->Telephone_Number, (int) $transaction->Amount, $transaction->TXN_ID, $transaction->Type);

      $this->info('Response');
      $this->info(json_encode($response, true));
      return 0;
    } catch (\Throwable $th) {
      $this->error($th->getMessage());
      return 1;
    }
  }
}
