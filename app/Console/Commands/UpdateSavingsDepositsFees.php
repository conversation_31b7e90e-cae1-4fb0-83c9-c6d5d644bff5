<?php

namespace App\Console\Commands;

use App\Models\SavingsProductFee;
use App\Models\Transactables\SavingsDeposit;
use Illuminate\Console\Command;

class UpdateSavingsDepositsFees extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:update-savings-deposits-fees';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $deposits = SavingsDeposit::all();
        $savingProductFees = SavingsProductFee::select('id', 'value', 'minimum_amount', 'maximum_amount')->get();
        $count = 0;
        foreach ($deposits as $deposit) {
            foreach ($savingProductFees as $fee) {
                if ($deposit->amount >= $fee->minimum_amount && $deposit->amount <= $fee->maximum_amount) {
                    $deposit->savings_product_fee_id = $fee->id;
                    $deposit->save();
                    $count += 1;
                    continue;
                }
            }
        }

        // dd($count);
    }
}
