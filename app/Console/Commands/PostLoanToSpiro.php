<?php

namespace App\Console\Commands;

use App\Models\Customer;
use App\Models\Loan;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PostLoanToSpiro extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'lms:post-loan-to-spiro {--loanId=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {

            $loan = Loan::find($this->option('loanId'));
            if (!$loan) {
                $this->error('Loan to post to SPIRO could not be found');
                return;
            }
            $customer = Customer::find($loan->Customer_ID);
            if (!$customer) {
                $this->error('Customer for loan to post to SPIRO could not be found');
                return;
            }
            $postData = [
                "referenceId" => "GNU_ID_" . $loan->id,
                "msisdn" => $customer->Telephone_Number,
                "firstName" => $customer->First_Name,
                "lastName" => $customer->Last_Name,
                "nationalIdNumber" => $customer->ID_Number,
                "loanContractId" => 'LN_ID' . $loan->id,
                "startDate" => $loan->Date_of_First_Payment->format('Y-m-d'),
                "maturityDate" => $loan->Maturity_Date->format('Y-m-d'),
                "loanAmount" =>  $loan->Credit_Amount,
                "fundTransferReference" => 'DFCU-TRX-' . $loan->loan_application->Approval_Reference,
                "loanApprovalStatus" => "Approved",
                "message" => "Loan approved successfully"
            ];
            $accessToken = $this->getAccessToken();
            // Step 2: Use the access token to call the Loan Market API
            $apiResponse = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'bearer-token' => $accessToken,
                'asset-financier-id' => 'UG-AF-20250513-0000020'
            ])->post(
                env('SPIRO_URL') . '/loanConfirmation',
                $postData
            );
            $message = '';
            if ($apiResponse->successful()) {
                $message = 'API call successful: ' . $apiResponse->body();
            } else {
                $message = 'API failure: ' . $apiResponse->body();
            }
            $this->info($message);

            return;
        } catch (Exception $e) {
            Log::error($e->getMessage());
            $this->error($e->getMessage());
            return;
        }
    }

    protected function getAccessToken(): string
    {
        $accessToken = Cache::get('spiro_access_token');
        if ($accessToken) {
            return $accessToken;
        } else {
            $tokenResponse = Http::withHeaders([
                'grant_type' => 'client_credentials',
                'client-id' => env('SPIRO_CLIENT_ID'),
                'client-secret' => env('SPIRO_CLIENT_SECRET'),
                'refresh-token' => env('SPIRO_REFRESH_TOKEN'),
            ])->get(env('SPIRO_URL') . '/oauth/token');
            if ($tokenResponse->successful()) {
                $accessToken = $tokenResponse->json()['access_token'];
                $tokenLifeTime = $tokenResponse->json()['expires_in']; // seconds
                Cache::put('spiro_access_token', $accessToken, $tokenLifeTime);
                return $accessToken;
            } else {
                $this->error('Failed to retrieve access token: ' . $tokenResponse->body());
                throw new Exception('Failed to get access token');
            }
        }
    }
}
