<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class SmsNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $message;
    protected $phoneNumber;
    public $partnerID;
    protected $customerID;

    public function __construct($message, $phoneNumber, $customerID, $partnerID)
    {
        $this->message = $message;
        $this->phoneNumber = $phoneNumber;
        $this->partnerID = $partnerID;
        $this->customerID = $customerID;
    }

    public function via(object $notifiable): array
    {
        return ['sms', 'database'];
    }

    public function toDatabase($notifiable): array
    {
        return [
            'message' => $this->message,
            'phoneNumber' => $this->phoneNumber,
            'partnerID' => $this->partnerID,
            'customerID' => $this->customerID
        ];
    }

    public function toSms($notifiable)
    {
        return $this->message;
    }

    public function shouldSend(object $notifiable, string $channel): bool
    {
        // if (app()->isProduction() == false) {
        //     return false;
        // }
        // return app()->isProduction();
        return true;
    }
}
