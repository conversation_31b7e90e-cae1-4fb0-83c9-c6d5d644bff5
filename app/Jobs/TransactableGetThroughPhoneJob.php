<?php

namespace App\Jobs;

use App\Models\Transaction;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use App\Services\PaymentServiceManager;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class TransactableGetThroughPhoneJ<PERSON> implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $amount;
    public $fee;
    public $reason;
    public $txn_request;
    public $phone_number;
    public $txn_reference;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Transaction $txn_request, $reason)
    {
        $this->txn_request = $txn_request;
        $this->phone_number = $txn_request->Telephone_Number;
        $this->amount = $txn_request->Amount;
        $this->fee = $txn_request->savings_product_fee ?  $txn_request->savings_product_fee->value : 0;
        $this->reason = $reason;
        $this->txn_reference = $txn_request->TXN_ID;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            /**
             * Down Payment transaction must have an asset_provider_id,
             * Ensure this before this transaction reaches here.
             */
            $paymentService = (new PaymentServiceManager($this->txn_request))->paymentService;

            $response = $paymentService->collect($this->phone_number, $this->amount + $this->fee, $this->txn_reference, $this->reason);

            $this->txn_request->update([
                // 'Status' => $response['message'],
                'Provider_TXN_ID' => $response['reference']
            ]);
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
            Log::error($th->getTraceAsString());
        }
    }
}
