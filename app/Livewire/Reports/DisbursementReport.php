<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetCreditBorrowerAccountReportDetailsAction;
use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Enums\LoanAccountType;
use App\Enums\LoanApplicationStatus;
use App\Enums\MaritalStatus;
use App\Enums\PaymentFrequency;
use App\Exports\DisbursementExport;
use App\Models\Loan;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Storage;
use League\Csv\CannotInsertRecord;
use League\Csv\Exception;
use League\Csv\InvalidArgument;
use League\Csv\UnavailableStream;
use League\Csv\Writer;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class DisbursementReport extends Component
{
    use ExportsData, WithPagination;

    public function mount()
    {
        $this->startDate = now()->startOfMonth()->format('Y-m-d');
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.disbursement-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        $viewData = [
            'records' => app(GetDisbursementReportDetailsAction::class)
                ->filters($this->getFilters())
                ->execute(),
            'partnerName' => auth()->user()?->partner->Institution_Name,
            'filters' => $this->getFormattedDateFilters()
        ];

        return app(PdfGeneratorService::class)
            ->view('pdf.loan-disbursements', $viewData)
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new DisbursementExport($this->getFilters()), $this->getFilename());
    }

    /**
     * @throws UnavailableStream
     * @throws InvalidArgument
     */
    public function exportCba()
    {
        $path = 'partners/' . auth()->user()->partner->Identification_Code . '/' . $this->getCBAFilename();
        $csvFile = Writer::createFromString()->setDelimiter('|')->setEnclosure('"');

        $filters = [
            'startDate' => now()->subMonth()->startOfMonth()->format('Y-m-d'),
            'endDate' => now()->subMonth()->endOfMonth()->format('Y-m-d'),
        ];

        $recordQuery = app(GetCreditBorrowerAccountReportDetailsAction::class)
            ->filters($filters)
            ->execute();

        $headerRecord = [
            "H",
            auth()->user()->partner->Identification_Code,
            auth()->user()->partner->Institution_Name,
            now()->subMonth()->endOfMonth()->format('Ymd'),
            "8.0",
            now()->format('Ymd'),
            "CBA"
        ];

        try {
            $csvFile->insertOne($headerRecord);

            $recordQuery->lazy()
                ->each(function (Loan $loan) use ($csvFile) {
                    $csvFile->insertOne($this->getCbaRecord($loan));
                });

            Storage::disk('local')->put($path, $csvFile->toString());

            return response()->download(storage_path('app/'.$path));
        } catch (CannotInsertRecord|Exception $e) {
            return redirect()
                ->to('/reports/loans/disbursement')
                ->with('error', 'There was an error exporting the CBA file. Please try again.');
        }
    }

    private function getReportData()
    {
        if (empty($this->endDate) || empty($this->startDate)) {
            return collect();
        }

        return app(GetDisbursementReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }

    /**
     * @return array
     */
    public function getFilters(): array
    {
        return [
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }

    /**
     * @return string
     */
    public function getFilename(): string
    {
        return str(self::class)->afterLast('\\')->snake()->toString() . now()->toDateString() . '.xlsx';
    }

    private function getCBAFilename(): string
    {
        return auth()->user()->partner->Identification_Code .
            now()->subMonth()->endOfMonth()->format('Ymd') .
            'CBA.CSV';
    }

    private function getCbaRecord(Loan $loan): array
    {
        /**
         * 'pi_identification_code',
         * 'branch_identification_code',
         * 'borrowers_client_number',
         * 'borrower_classification',
         * 'credit_account_reference',
         * 'credit_account_date',
         * 'credit_amount',
         * 'credit_amount_ugx_equivalent',
         * 'facility_amount_granted',
         * 'credit_amount_drawdown',
         * 'credit_amount_drawdown_ugx_equivalent',
         * 'credit_account_type',
         * 'group_identification_joint_account_number',
         * 'transaction_date',
         * 'currency',
         * 'opening_balance_indicator',
         * 'maturity_date',
         * 'type_of_interest',
         * 'interest_calculation_method',
         * 'annual_interest_rate_at_disbursement',
         * 'annual_interest_rate_at_reporting',
         * 'date_of_first_payment',
         * 'credit_amortization_type',
         * 'credit_payment_frequency',
         * 'number_of_payments',
         * 'monthly_instalment_amount',
         * 'current_balance_amount',
         * 'current_balance_amount_ugx_equivalent',
         * 'current_balance_indicator',
         * 'last_payment_date',
         * 'last_payment_amount',
         * 'credit_account_status',
         * 'last_status_change_date',
         * 'credit_account_risk_classification',
         * 'credit_account_arrears_date',
         * 'number_of_days_in_arrears',
         * 'balance_overdue',
         * 'flag_for_restructured_credit',
         * 'old_branch_code',
         * 'old_account_number',
         * 'old_client_number',
         * 'old_pi_identification_code',
         * 'credit_account_closure_date',
         * 'credit_account_closure_reason',
         * 'specific_provision_amount',
         * 'client_consent_flag',
         * 'client_advice_notice_flag',
         * 'term',
         * 'loan_purpose',
         * 'group_joint_account_exposure_amount',
         * 'flag_for_group',
         * 'flag_for_joint_account',
         * 'mode_of_restructure',
         * 'risk_classification_criteria',
         * 'ii_registration_certificate_number',
         * 'ii_tax_identification_number',
         * 'ii_fcs_number',
         * 'ii_passport_number',
         * 'ii_drivers_licence_id_number',
         * 'ii_drivers_license_permit_number',
         * 'ii_country_id',
         * 'ii_country_issuing_authority',
         * 'ii_nationality',
         * 'ii_country_of_issue',
         * 'ii_refugee_number',
         * 'ii_work_permit_number',
         * 'gscafb_business_name',
         * 'gscafb_trading_name',
         * 'gscafb_activity_description',
         * 'gscafb_industry_sector_code',
         * 'gscafb_date_registered',
         * 'gscafb_business_type_code',
         * 'gscafb_surname',
         * 'gscafb_forename1',
         * 'gscafb_forename2',
         * 'gscafb_forename3',
         * 'gscafb_gender',
         * 'gscafb_marital_status',
         * 'gscafb_date_of_birth',
         * 'ei_employment_type',
         * 'ei_primary_occupation',
         * 'ei_employer_name',
         * 'ei_employee_number',
         * 'ei_employment_date',
         * 'ei_income_band',
         * 'ei_salary_frequency',
         * 'pci_unit_number',
         * 'pci_building_name',
         * 'pci_floor_number',
         * 'pci_plot_or_street_number',
         * 'pci_lc_or_street_name',
         * 'pci_parish',
         * 'pci_suburb',
         * 'pci_village',
         * 'pci_county_or_town',
         * 'pci_district',
         * 'pci_region',
         * 'pci_po_box_number',
         * 'pci_post_office_town',
         * 'pci_country_code',
         * 'pci_period_at_address',
         * 'pci_flag_of_ownership',
         * 'pci_primary_number_country_dialling_code',
         * 'pci_primary_number_telephone_number',
         * 'pci_other_number_country_dialling_code',
         * 'pci_other_number_telephone_number',
         * 'pci_mobile_number_country_dialling_code',
         * 'pci_mobile_number_telephone_number',
         * 'pci_facsimile_country_dialling_code',
         * 'pci_facsimile_number',
         * 'pci_email_address',
         * 'pci_web_site',
         * 'pci_latitude',
         * 'pci_longitude',
         * 'sci_unit_number',
         * 'sci_unit_name',
         * 'sci_floor_number',
         * 'sci_plot_or_street_number',
         * 'sci_lc_or_street_name',
         * 'sci_parish',
         * 'sci_suburb',
         * 'sci_village',
         * 'sci_county_or_town',
         * 'sci_district',
         * 'sci_region',
         * 'sci_po_box_number',
         * 'sci_post_office_town',
         * 'sci_country_code',
         * 'sci_period_at_address',
         * 'sci_flag_for_ownership',
         * 'sci_primary_number_country_dialling_code',
         * 'sci_primary_number_telephone_number',
         * 'sci_other_number_country_dialling_code',
         * 'sci_other_number_telephone_number',
         * 'sci_mobile_number_country_dialling_code',
         * 'sci_mobile_number_telephone_number',
         * 'sci_facsimile_country_dialling_code',
         * 'sci_facsimile_number',
         * 'sci_email_address',
         * 'sci_web_site',
         * 'sci_latitude',
         * 'sci_longitude',
         */
        $repayments = $loan->loan_repayments;

        return [
            "D",
            auth()->user()->partner->Identification_Code, // pi_identification_code
            "001", // Branch identification code
            $loan->Customer_ID, // Borrowers client number
            "0", // Borrower classification
            $loan->account_number, // Credit account reference
            $loan->Credit_Account_Date->format('Ymd'), // Credit account date
            $loan->Credit_Amount, // Credit amount
            $loan->Credit_Amount, // Credit amount ugx equivalent
            $loan->Facility_Amount_Granted, // Facility amount granted
            $loan->Credit_Amount_Drawdown, // Credit amount drawdown
            $loan->Credit_Amount_Drawdown, // Credit amount drawdown ugx equivalent
            $loan->Credit_Account_Type,
            null,
            $loan->Credit_Account_Date->format('Ymd'), // Transaction date
            $loan->Currency, // Currency
            '0', // Opening balance indicator
            $loan->Maturity_Date->format('Ymd'), // Maturity date
            $loan->Type_of_Interest, // Type of interest
            $this->getInterestCalculationMethodCode($loan->loan_term->Interest_Calculation_Method), // Interest calculation method
            $loan->Annual_Interest_Rate_at_Disbursement, // Annual interest rate at disbursement
            $loan->Annual_Interest_Rate_at_Disbursement, // Annual interest rate at reporting
            $repayments->first()?->Transaction_Date->format('Ymd'), // Date of first payment
            $loan->Credit_Amortization_Type, // Credit amortization type
            PaymentFrequency::getValueFromName($loan->Credit_Payment_Frequency),
            $loan->Number_of_Payments,
            $loan->schedule->groupBy('installment_number')->first()->sum('total_payment'), // Monthly instalment amount
            ($balance = $loan->totalOutstandingBalance()), // Current balance amount
            $balance, // Current balance amount ugx equivalent
            '0', // Current balance indicator
            $repayments->last()?->Transaction_Date->format('Ymd'), // Last payment date
            $repayments->last()?->amount, // Last payment amount
            $loan->Credit_Account_Status, // Credit account status
            $loan->Last_Status_Change_Date->format('Ymd'), // Last status change date
            '5', // Credit account risk classification // todo: get risk classification
            $loan->Credit_Account_Status === LoanAccountType::WrittenOff->value ? $loan->Written_Off_Date?->format('Ymd') : null, // Credit account arrears date
            $loan->Credit_Account_Status === LoanAccountType::WrittenOff->value ? $loan->Written_Off_Date?->diffInDays($loan->Maturity_Date, true) : null, // Number of days in arrears
            $loan->Credit_Account_Status === LoanAccountType::WrittenOff->value ? $loan->Written_Off_Amount - $loan->Written_Off_Amount_Recovered : 0, // Balance overdue
            '1', // Flag for restructured credit
            null, // Old branch code
            null, // Old account number
            null, // Old client number
            null, // Old pi identification code
            $loan->Credit_Account_Closure_Date?->format('Ymd'), // Credit account closure date
            $loan->Credit_Account_Closure_Reason, // Credit account closure reason
            null, // Specific provision amount
            'Y', // Client consent flag
            'Y', // Client advice notice flag
            $loan->Term,
            '1207',
            null, // Group joint account
            '1', // Flag for group
            '1', // Flag for joint account
            null, // Mode of restructure
            '0', // Risk classification criteria
            null, // Registration certificate number
            null, // Tax identification number
            null, // FCS number
            null, // Passport number
            null, // Driver's license ID number
            null, // Driver's license permit number
            $loan->customer->ID_Number,
            'UG', // Country issuing authority
            'UG', // Nationality
            'UG', // Country of issue
            null, // Refugee number
            null, // Work permit number
            null, // Business name
            null, // Trading name
            null, // Activity description
            null, // Industry sector code
            null, // Date registered
            null, // Business type code
            $loan->customer->Last_Name,
            $loan->customer->First_Name,
            $loan->customer->Other_Name,
            null, // Forename3
            strtolower($loan->customer->Gender) === 'male' ? 0 : 1,
            MaritalStatus::getValueFromName($loan->customer->Marital_Status),
            $loan->customer->Date_of_Birth->format('Ymd'),
            3, // Employment type
            null, // Primary occupation
            null, // Employer name
            null, // Employee number
            null, // Employment date
            null, // Income band
            null, // Salary frequency
            null, // Unit number
            null, // Building name
            null, // Floor number
            null, // Plot or street number
            null, // LC or street name
            'KAMPALA', // Parish
            'KAMPALA', // Suburb
            'KAMPALA', // Village
            'KAMPALA', // County or town
            'KAMPALA', // District
            '0', // Region
            null, // PO box number
            null, // Post office town
            'UG', // Country code
            '1', // Period at address
            'T', // Flag of ownership
            '256', // Primary number country dialling code
            str($loan->customer->Telephone_Number)->after('256')->toString(),
            null, // Other number country dialling code
            null, // Other number telephone number
            null, // Mobile number country dialling code
            null, // Mobile number telephone number
            null, // Facsimile country dialling code
            null, // Facsimile number
            null, // Email address
            null, // Web site
            null, // Latitude
            null, // Longitude
            null, // Secondary contact unit number
            null, // Secondary contact unit name
            null, // Secondary contact floor number
            null, // Secondary contact plot or street number
            null, // Secondary contact LC or street name
            null, // Secondary contact parish
            null, // Secondary contact suburb
            null, // Secondary contact village
            null, // Secondary contact county or town
            null, // Secondary contact district
            null, // Secondary contact region
            null, // Secondary contact PO box number
            null, // Secondary contact post office town
            null, // Secondary contact country code
            null, // Secondary contact period at address
            null, // Secondary contact flag for ownership
            '256', // Secondary contact primary number country dialling code
            str($loan->customer->Telephone_Number)->after('256')->toString(),
            null, // Secondary contact other number country dialling code
            null, // Secondary contact other number telephone number
            null, // Secondary contact mobile number country dialling code
            null, // Secondary contact mobile number telephone number
            null, // Secondary contact facsimile country dialling code
            null, // Secondary contact facsimile number
            null, // Secondary contact email address
            null, // Secondary contact website
            null, // Secondary contact latitude
            null, // Secondary contact longitude
        ];
    }

    private function getLoanStatusCode(?int $status): ?int
    {
        return match ($status) {
            Loan::ACCOUNT_STATUS_CURRENT_AND_WITHIN_TERMS => 0, // Active/Current
            Loan::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS => 1, // In arrears
            Loan::ACCOUNT_STATUS_WRITTEN_OFF => 3, // Written off
            Loan::ACCOUNT_STATUS_FULLY_PAID_OFF => 4, // Fully paid
            default => null
        };
    }

    private function getInterestTypeCode(string $interestCalculationMethod): int
    {
        if ($interestCalculationMethod === 'Flat') {
            return 0;
        } else {
            return 1;
        }
    }

    private function getInterestCalculationMethodCode(string $interestCalculationMethod): int
    {
        if ($interestCalculationMethod === 'Flat') {
            return 1;
        } else {
            return 0;
        }
    }
}
