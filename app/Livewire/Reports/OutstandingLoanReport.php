<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetOutstandingLoanReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\OutstandingLoanExport;

class OutstandingLoanReport extends Component
{
    use WithPagination, ExportsData;

    public function mount()
    {
        $this->endDate = now()->format('Y-m-d');
    }

    public function render()
    {
        return view('livewire.reports.outstanding-loans-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function printReport()
    {
        return app(PdfGeneratorService::class)
            ->view('pdf.outstanding-loans', [
                'records' => app(GetOutstandingLoanReportDetailsAction::class)
                    ->filters($this->getFilters())
                    ->execute(),
                'partnerName' => auth()->user()->partner->Institution_Name,
                'filters' => $this->getFormattedDateFilters()
            ])
            ->streamFromLivewire();
    }

    public function excelExport()
    {
        return Excel::download(new OutstandingLoanExport($this->getFilters()), $this->getExcelFilename());
    }

    private function getReportData()
    {
        if (empty($this->endDate)) {
            return collect();
        }

        return app(GetOutstandingLoanReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }
}
