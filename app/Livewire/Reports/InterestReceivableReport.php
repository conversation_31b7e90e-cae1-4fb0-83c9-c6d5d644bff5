<?php

namespace App\Livewire\Reports;

use App\Actions\Reports\GetDisbursementReportDetailsAction;
use App\Actions\Reports\GetInterestReceivableReportDetailsAction;
use App\Actions\Reports\GetRepaymentReportDetailsAction;
use App\Exports\DisbursementExport;
use App\Exports\InterestReceivableExport;
use App\Services\PdfGeneratorService;
use App\Traits\ExportsData;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Carbon;
use Livewire\Component;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;

class InterestReceivableReport extends Component
{
    use ExportsData, WithPagination;

    public function mount()
    {
        $this->startDate = now()->toDateString();
        $this->endDate = now()->toDateString();
    }

    public function render()
    {
        return view('livewire.reports.interest-receivable-report', [
            'records' => $this->getReportData()
        ]);
    }

    public function updatedStartDate($value)
    {
        if (empty($value)) {
            $this->startDate = now()->toDateString();
        }
    }

    public function updatedEndDate($value)
    {
        if (empty($value) || Carbon::parse($value)->isFuture()) {
            $this->endDate = now()->toDateString();
        }
    }

    public function printReport()
    {
        $viewData = [
            'records' => app(GetInterestReceivableReportDetailsAction::class)
                ->filters($this->getFilters())
                ->execute(),
            'partnerName' => auth()->user()?->partner->Institution_Name,
            'filters' => $this->getFormattedDateFilters()
        ];

        return app(PdfGeneratorService::class)
            ->view('pdf.interest-receivable', $viewData)
            ->streamFromLivewire();
    }

    public function excelExport(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        return Excel::download(new InterestReceivableExport($this->getFilters()), $this->getFilename());
    }

    private function getReportData()
    {
        return app(GetInterestReceivableReportDetailsAction::class)
            ->paginate()
            ->filters($this->getFilters())
            ->execute();
    }

    /**
     * @return array
     */
    public function getFilters(): array
    {
        return [
            'startDate' => $this->startDate,
            'endDate' => $this->endDate,
        ];
    }

    /**
     * @return string
     */
    public function getFilename(): string
    {
        return str(self::class)->afterLast('\\')->snake()->toString() . now()->toDateString() . '.xlsx';
    }
}
