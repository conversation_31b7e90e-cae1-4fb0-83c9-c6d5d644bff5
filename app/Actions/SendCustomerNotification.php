<?php

namespace App\Actions;

use App\Models\Customer;
use App\Models\CustomerAsset;
use App\Notifications\SmsNotification;
use Illuminate\Support\Facades\Log;

class SendCustomerNotification
{
    public function execute(CustomerAsset $asset): void
    {
        $asset->customer->notify(new SmsNotification('Hello ' . $asset->customer->name . ', your logbook is ready for collection. Please visit the nearest SPIRO branch to collect it.', $asset->customer->Telephone_Number, $asset->customer->id, $asset->loanApplication->Partner_ID));

        try {
            $asset->setStatus('Notified', 'Pickup logbook');
        } catch (\Throwable $th) {
            Log::error($th->getMessage());
        }
        // todo: Update CustomerAsset to indicate date this notification was sent.
    }
}
