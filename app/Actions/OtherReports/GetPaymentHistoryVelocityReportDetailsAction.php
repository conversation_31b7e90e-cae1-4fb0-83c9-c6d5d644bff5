<?php

namespace App\Actions\OtherReports;

use App\Models\Loan;
use App\Models\LoanRepayment;
use App\Models\LoanSchedule;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class GetPaymentHistoryVelocityReportDetailsAction
{
    protected string $startDate = '';
    protected string $endDate = '';
    protected int $perPage = 0;
    protected int $loanId = 0;

    public function filters(array $filters): self
    {
        $this->startDate = $filters['startDate'] ?? '';
        $this->endDate = $filters['endDate'] ?? '';

        return $this;
    }

    public function paginate(int $perPage = 15): self
    {
        $this->perPage = $perPage;
        return $this;
    }

    public function execute(): \Illuminate\Pagination\LengthAwarePaginator|Collection
    {
        $query = $this->buildQuery();

        if ($this->perPage > 0) {
            return $query->paginate($this->perPage);
        }

        return $query->get();
    }

    private function buildQuery()
    {
        // Get loan schedules with their corresponding repayments
        // We'll calculate payment velocity by comparing due dates with actual payment dates
        $query = DB::table('loan_schedules as ls')
            ->join('loans as l', 'ls.loan_id', '=', 'l.id')
            ->join('customers as c', 'l.Customer_ID', '=', 'c.id')
            //->join('loan_repayments as lr', 'l.id', '=', 'lr.Loan_ID')
            ->select([
                'l.id as loan_id',
                'l.Credit_Account_Reference',
                'c.First_Name',
                'c.Last_Name',
                'c.Telephone_Number',
                'ls.installment_number',
                'ls.payment_due_date',
                'ls.total_payment as installment_amount',
                'ls.payment_due_date as schedule_updated_at',
                'ls.updated_at as payment_date',
                DB::raw('ls.total_payment - ls.total_outstanding payment_amount'),
                DB::raw('DATEDIFF(DATE(ls.updated_at), DATE(ls.payment_due_date)) as days_difference'),
                DB::raw('CASE
                    WHEN DATEDIFF(DATE(ls.updated_at), DATE(ls.payment_due_date)) < 0 THEN "Early"
                    WHEN DATEDIFF(DATE(ls.updated_at), DATE(ls.payment_due_date)) = 0 THEN "On Time"
                    ELSE "Late"
                END as payment_timing'),
                DB::raw('ABS(DATEDIFF(DATE(ls.updated_at), DATE(ls.payment_due_date))) as abs_days_difference')
            ])
            ->where('l.id', $this->loanId)
            ->where('ls.total_outstanding', '<=', 0)
            ->whereNotNull('ls.updated_at')
            ->orderBy('l.id')
            ->orderBy('ls.installment_number');

        // Apply date filters if provided
        if (!empty($this->startDate)) {
            $query->where('ls.updated_at', '>=', $this->startDate);
        }

        if (!empty($this->endDate)) {
            $query->where('ls.updated_at', '<=', $this->endDate . ' 23:59:59');
        }

        return $query;
    }

    /**
     * Get payment velocity trends for a specific loan
     */
    public function getLoanPaymentTrend(int $loanId): Collection
    {
        return DB::table('loan_schedules as ls')
            ->join('loan_repayments as lr', 'ls.loan_id', '=', 'lr.Loan_ID')
            ->select([
                'ls.installment_number',
                'ls.payment_due_date',
                'lr.Transaction_Date as payment_date',
                DB::raw('DATEDIFF(DATE(lr.Transaction_Date), DATE(ls.payment_due_date)) as days_difference'),
                DB::raw('CASE
                    WHEN DATEDIFF(DATE(lr.Transaction_Date), DATE(ls.payment_due_date)) < 0 THEN "Early"
                    WHEN DATEDIFF(DATE(lr.Transaction_Date), DATE(ls.payment_due_date)) = 0 THEN "On Time"
                    ELSE "Late"
                END as payment_timing')
            ])
            ->where('ls.loan_id', $loanId)
            ->where('ls.total_outstanding', '<=', 0)
            ->whereNotNull('lr.Transaction_Date')
            ->orderBy('ls.installment_number')
            ->get();
    }

    /**
     * Get summary statistics for the report
     */
    public function getSummaryStats(): array
    {
        $baseQuery = $this->buildQuery();

        $stats = $baseQuery->selectRaw('
            COUNT(*) as total_payments,
            AVG(DATEDIFF(DATE(lr.Transaction_Date), DATE(ls.payment_due_date))) as avg_days_difference,
            SUM(CASE WHEN DATEDIFF(DATE(lr.Transaction_Date), DATE(ls.payment_due_date)) < 0 THEN 1 ELSE 0 END) as early_payments,
            SUM(CASE WHEN DATEDIFF(DATE(lr.Transaction_Date), DATE(ls.payment_due_date)) = 0 THEN 1 ELSE 0 END) as on_time_payments,
            SUM(CASE WHEN DATEDIFF(DATE(lr.Transaction_Date), DATE(ls.payment_due_date)) > 0 THEN 1 ELSE 0 END) as late_payments
        ')->first();

        return [
            'total_payments' => $stats->total_payments ?? 0,
            'avg_days_difference' => round($stats->avg_days_difference ?? 0, 2),
            'early_payments' => $stats->early_payments ?? 0,
            'on_time_payments' => $stats->on_time_payments ?? 0,
            'late_payments' => $stats->late_payments ?? 0,
            'early_percentage' => $stats->total_payments > 0 ? round(($stats->early_payments / $stats->total_payments) * 100, 2) : 0,
            'on_time_percentage' => $stats->total_payments > 0 ? round(($stats->on_time_payments / $stats->total_payments) * 100, 2) : 0,
            'late_percentage' => $stats->total_payments > 0 ? round(($stats->late_payments / $stats->total_payments) * 100, 2) : 0,
        ];
    }

    public function forLoan(int $loanId)
    {
        $this->loanId = $loanId;

        return $this;
    }
}
