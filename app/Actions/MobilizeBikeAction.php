<?php

namespace App\Actions;

use App\Models\CustomerAsset;
use App\Services\SpiroApiService;

class MobilizeBikeAction
{
    public function execute($bikeNumber)
    {
        try {
            $bikeProvider = new SpiroApiService();

            $bikeProvider->mobilizeBike($bikeNumber);

            $asset = CustomerAsset::query()->firstWhere('Identification', $bikeNumber);

            if ($asset) {
                $asset->update([
                    'Disabled_At' => null,
                ]);
            }

            logger()->info("Bike $bikeNumber successfully mobilized");

        } catch (\Throwable $th) {
            logger()->error($th->getMessage());
            return false;
        }
    }
}
