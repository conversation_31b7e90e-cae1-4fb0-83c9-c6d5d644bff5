<?php

namespace App\Actions\Loans;

use App\Enums\CreditPaymentFrequency;
use App\Models\Loan;
use App\Models\Switches;
use Illuminate\Database\Eloquent\Builder;

// Mark loans as overdue if they are past their maturity date
class MarkOverDueLoansAction
{
    public function execute(): void
    {
        $query = Loan::query()
            ->whereBeforeToday('Maturity_Date')
            ->where('Credit_Account_Status', Loan::ACCOUNT_STATUS_CURRENT_AND_WITHIN_TERMS);

        $query->update([
            'Credit_Account_Status' => Loan::ACCOUNT_STATUS_OUTSTANDING_AND_BEYOND_TERMS,
            'Last_Status_Change_Date' => now()->format('Y-m-d'),
        ]);

        $loanIds = $query->pluck('id');

        /**
         * Find loans that are on the MTN switch and the auto-sweep has not been started
         * Autosweep them
         */
        $switch = Switches::query()
            ->where('category', 'Payment')
            ->where('status', 'On')
            ->where('name', 'MTN')
            ->first();

        if (empty($switch)) {
            return;
        }

        Loan::query()
            ->find($loanIds)
            ->whereNull('Auto_Sweep_Started')
            ->whereHas('loan_product', function (Builder $query) use ($switch) {
                return $query->where('Switch_ID', $switch->id);
            })
            ->each(function (Loan $loan) {
                \App\Jobs\AutoSweepLoan::dispatch($loan->id);
            });
    }
}
