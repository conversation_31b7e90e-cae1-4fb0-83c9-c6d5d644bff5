<?php

namespace App\Actions\Loans;

use App\Models\CustomerAsset;
use App\Events\LoanAssetReposessed;

class ReposessAssetAction
{
    public function execute($identification, $loanId)
    {
        $customerAsset = CustomerAsset::query()
            ->where('identification', $identification)
            ->where('Loan_ID', $loanId)->first();

        if (empty($customerAsset)) {
            return;
        }

        $customerAsset->update([
            'Reposessed_Date' => now(),
            'Reposessed_By' => auth()->id(),
            'Status' => 'Reposessed',
        ]);

        event(new LoanAssetReposessed($customerAsset));
    }
}
