<?php

namespace App\Actions;

use App\Models\Customer;
use App\Models\CustomerAsset;
use App\Services\SpiroApiService;

class RegisterCustomerAction
{
    protected bool $usingMtnKyc = false;

    /**
     * @throws \Exception
     */
    public function execute(array $details): Customer
    {
        if ($this->usingMtnKyc) {
            return $this->registerWithMtnKyc($details);
        }

        throw new \Exception('No KYC provider selected');
    }

    public function useMtnKyc(): self
    {
        $this->usingMtnKyc = true;

        return $this;
    }

    protected function registerWithMtnKyc(array $details): Customer
    {
        $phoneNumber = str(data_get($details, 'resource'))->after('FRI:')->before('/')->toString();
        $record = [
            'First_Name' => data_get($details,'name.firstname'),
            'Last_Name' => data_get($details, 'name.lastname'),
            'Gender' => data_get($details, 'gender'),
            'Date_of_Birth' => data_get($details, 'dob'),
            'ID_Type' => data_get($details, 'idtype'),
            'ID_Number' => data_get($details, 'idnumber'),
            'Classification' => 'Individual',
            'Telephone_Number' => $phoneNumber,
        ];

        return Customer::query()->updateOrCreate([
            'Telephone_Number' => $phoneNumber,
        ], $record);
    }
}
